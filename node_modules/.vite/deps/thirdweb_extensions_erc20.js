import {
  convertErc20Amount
} from "./chunk-C3RVB2OS.js";
import {
  isSetContractURISupported
} from "./chunk-CXCCZTWP.js";
import {
  MerkleTree,
  hashEntry
} from "./chunk-2ZAZZPYX.js";
import {
  claimTo,
  isClaimToSupported
} from "./chunk-2AGHXURD.js";
import {
  getClaimParams
} from "./chunk-ROIIFOGK.js";
import {
  getApprovalForTransaction
} from "./chunk-ZWGM5P54.js";
import {
  getActiveClaimCondition,
  getActiveClaimConditionId,
  getClaimConditionById,
  isGetActiveClaimConditionIdSupported,
  isGetActiveClaimConditionSupported,
  isGetClaimConditionByIdSupported
} from "./chunk-U4FNJKQQ.js";
import {
  balanceOf,
  getBalance,
  isBalanceOfSupported
} from "./chunk-SE4ZMSPP.js";
import {
  upload
} from "./chunk-NT4PMLR5.js";
import {
  isContractURISupported
} from "./chunk-B5BMXGEI.js";
import {
  encodeTransfer,
  isTransferSupported,
  transfer
} from "./chunk-GHMPF3KL.js";
import {
  getCurrencyMetadata
} from "./chunk-F6FEGZJS.js";
import {
  isNameSupported,
  isSymbolSupported,
  name
} from "./chunk-PF3ESLX3.js";
import "./chunk-3PSI24KF.js";
import "./chunk-43F4DZXD.js";
import {
  approve
} from "./chunk-6IKHPL6Q.js";
import {
  allowance,
  isAllowanceSupported,
  isApproveSupported
} from "./chunk-T4QBBWYN.js";
import {
  decimals
} from "./chunk-7M5LIRA5.js";
import {
  isDecimalsSupported
} from "./chunk-BOY5NH7J.js";
import "./chunk-WWY7S4YD.js";
import {
  maxUint256
} from "./chunk-DUYIIKDP.js";
import {
  dateToSeconds,
  tenYearsFromNow
} from "./chunk-EP7Z72A6.js";
import {
  once
} from "./chunk-NACC2RRT.js";
import "./chunk-JMHMJ42H.js";
import {
  prepareEvent
} from "./chunk-OVBFYP7T.js";
import {
  NATIVE_TOKEN_ADDRESS,
  isNativeTokenAddress
} from "./chunk-YCZ3YGMG.js";
import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  prepareContractCall
} from "./chunk-Q44ZNVHQ.js";
import {
  readContract
} from "./chunk-A2MKHEYJ.js";
import "./chunk-TVIYKLDL.js";
import {
  extractErrorResult
} from "./chunk-JOYHE2MS.js";
import "./chunk-WDN7SIFG.js";
import {
  toUnits,
  toWei
} from "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import {
  randomBytesHex
} from "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import {
  stringify
} from "./chunk-2CIJO3V3.js";
import "./chunk-QGXAPRFG.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import {
  isHex,
  stringToHex,
  toHex
} from "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IERC20/read/totalSupply.js
var FN_SELECTOR = "0x18160ddd";
var FN_INPUTS = [];
var FN_OUTPUTS = [
  {
    type: "uint256"
  }
];
function isTotalSupplySupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
async function totalSupply(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: []
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IERC20/write/transferFrom.js
var FN_SELECTOR2 = "0x23b872dd";
var FN_INPUTS2 = [
  {
    type: "address",
    name: "from"
  },
  {
    type: "address",
    name: "to"
  },
  {
    type: "uint256",
    name: "value"
  }
];
var FN_OUTPUTS2 = [
  {
    type: "bool"
  }
];
function isTransferFromSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR2, FN_INPUTS2, FN_OUTPUTS2]
  });
}
function transferFrom(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR2, FN_INPUTS2, FN_OUTPUTS2],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [
        resolvedOptions.from,
        resolvedOptions.to,
        resolvedOptions.value
      ];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/read/isERC20.js
function isERC20(availableSelectors) {
  return [
    isNameSupported(availableSelectors),
    isSymbolSupported(availableSelectors),
    isDecimalsSupported(availableSelectors),
    isTotalSupplySupported(availableSelectors),
    isBalanceOfSupported(availableSelectors),
    isTransferSupported(availableSelectors),
    isTransferFromSupported(availableSelectors),
    isApproveSupported(availableSelectors),
    isAllowanceSupported(availableSelectors)
  ].every(Boolean);
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IVotes/read/delegates.js
var FN_SELECTOR3 = "0x587cde1e";
var FN_INPUTS3 = [
  {
    type: "address",
    name: "account"
  }
];
var FN_OUTPUTS3 = [
  {
    type: "address"
  }
];
async function delegates(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR3, FN_INPUTS3, FN_OUTPUTS3],
    params: [options.account]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IMintableERC20/write/mintTo.js
var FN_SELECTOR4 = "0x449a52f8";
var FN_INPUTS4 = [
  {
    type: "address",
    name: "to"
  },
  {
    type: "uint256",
    name: "amount"
  }
];
var FN_OUTPUTS4 = [];
function isMintToSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR4, FN_INPUTS4, FN_OUTPUTS4]
  });
}
function mintTo(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR4, FN_INPUTS4, FN_OUTPUTS4],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.to, resolvedOptions.amount];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/write/mintTo.js
function mintTo2(options) {
  return mintTo({
    contract: options.contract,
    asyncParams: async () => {
      let amount;
      if ("amount" in options) {
        const { decimals: decimals2 } = await import("./decimals-ADIRG3YK.js");
        const d = await decimals2(options).catch(() => 18);
        amount = toUnits(options.amount.toString(), d);
      } else {
        amount = options.amountWei;
      }
      return {
        to: options.to,
        amount,
        overrides: options.overrides
      };
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/write/transferFrom.js
function transferFrom2(options) {
  return transferFrom({
    contract: options.contract,
    asyncParams: async () => {
      let amount;
      if ("amount" in options) {
        const { decimals: decimals2 } = await import("./decimals-ADIRG3YK.js");
        const d = await decimals2(options).catch(() => 18);
        amount = toUnits(options.amount.toString(), d);
      } else {
        amount = options.amountWei;
      }
      return {
        from: options.from,
        to: options.to,
        value: amount,
        overrides: {
          erc20Value: {
            amountWei: amount,
            tokenAddress: options.contract.address
          },
          ...options.overrides
        }
      };
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/common/__generated__/IMulticall/write/multicall.js
var FN_SELECTOR5 = "0xac9650d8";
var FN_INPUTS5 = [
  {
    type: "bytes[]",
    name: "data"
  }
];
var FN_OUTPUTS5 = [
  {
    type: "bytes[]",
    name: "results"
  }
];
function isMulticallSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR5, FN_INPUTS5, FN_OUTPUTS5]
  });
}
function multicall(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR5, FN_INPUTS5, FN_OUTPUTS5],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.data];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/write/transferBatch.js
function transferBatch(options) {
  return multicall({
    contract: options.contract,
    asyncParams: async () => {
      const content = await optimizeTransferContent(options);
      return {
        data: content.map((item) => {
          return encodeTransfer({
            to: item.to,
            value: item.amountWei,
            overrides: {
              erc20Value: {
                amountWei: item.amountWei,
                tokenAddress: options.contract.address
              }
            }
          });
        })
      };
    }
  });
}
async function optimizeTransferContent(options) {
  const groupedRecords = await options.batch.reduce(async (accPromise, record) => {
    const acc = await accPromise;
    let amountInWei;
    if ("amount" in record) {
      const { decimals: decimals2 } = await import("./decimals-ADIRG3YK.js");
      const d = await decimals2(options).catch(() => void 0);
      if (d === void 0) {
        throw new Error(`Failed to get the decimals for contract: ${options.contract.address}`);
      }
      amountInWei = toUnits(record.amount.toString(), d);
    } else {
      amountInWei = record.amountWei;
    }
    const existingRecord = acc.find((r) => r.to.toLowerCase() === record.to.toLowerCase());
    if (existingRecord) {
      existingRecord.amountWei = existingRecord.amountWei + amountInWei;
    } else {
      acc.push({
        to: record.to,
        amountWei: amountInWei
      });
    }
    return acc;
  }, Promise.resolve([]));
  return groupedRecords;
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IBurnableERC20/write/burn.js
var FN_SELECTOR6 = "0x42966c68";
var FN_INPUTS6 = [
  {
    type: "uint256",
    name: "amount"
  }
];
var FN_OUTPUTS6 = [];
function burn(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR6, FN_INPUTS6, FN_OUTPUTS6],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.amount];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IBurnableERC20/write/burnFrom.js
var FN_SELECTOR7 = "0x79cc6790";
var FN_INPUTS7 = [
  {
    type: "address",
    name: "account"
  },
  {
    type: "uint256",
    name: "amount"
  }
];
var FN_OUTPUTS7 = [];
function burnFrom(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR7, FN_INPUTS7, FN_OUTPUTS7],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.account, resolvedOptions.amount];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IDropERC20/read/claimCondition.js
var FN_SELECTOR8 = "0xd637ed59";
var FN_INPUTS8 = [];
var FN_OUTPUTS8 = [
  {
    type: "uint256",
    name: "currentStartId"
  },
  {
    type: "uint256",
    name: "count"
  }
];
function isClaimConditionSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR8, FN_INPUTS8, FN_OUTPUTS8]
  });
}
async function claimCondition(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR8, FN_INPUTS8, FN_OUTPUTS8],
    params: []
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/drops/read/getClaimConditions.js
async function getClaimConditions(options) {
  try {
    const [startId, count] = await claimCondition(options);
    const conditionPromises = [];
    for (let i = startId; i < startId + count; i++) {
      conditionPromises.push(getClaimConditionById({
        ...options,
        conditionId: i
      }));
    }
    return Promise.all(conditionPromises);
  } catch {
    throw new Error("Claim condition not found");
  }
}
function isGetClaimConditionsSupported(availableSelectors) {
  return isClaimConditionSupported(availableSelectors) && isGetClaimConditionByIdSupported(availableSelectors);
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/DropERC20/read/verifyClaim.js
var FN_SELECTOR9 = "0x23a2902b";
var FN_INPUTS9 = [
  {
    type: "uint256",
    name: "_conditionId"
  },
  {
    type: "address",
    name: "_claimer"
  },
  {
    type: "uint256",
    name: "_quantity"
  },
  {
    type: "address",
    name: "_currency"
  },
  {
    type: "uint256",
    name: "_pricePerToken"
  },
  {
    type: "tuple",
    name: "_allowlistProof",
    components: [
      {
        type: "bytes32[]",
        name: "proof"
      },
      {
        type: "uint256",
        name: "quantityLimitPerWallet"
      },
      {
        type: "uint256",
        name: "pricePerToken"
      },
      {
        type: "address",
        name: "currency"
      }
    ]
  }
];
var FN_OUTPUTS9 = [
  {
    type: "bool",
    name: "isOverride"
  }
];
async function verifyClaim(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR9, FN_INPUTS9, FN_OUTPUTS9],
    params: [
      options.conditionId,
      options.claimer,
      options.quantity,
      options.currency,
      options.pricePerToken,
      options.allowlistProof
    ]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/drops/read/canClaim.js
async function canClaim(options) {
  const quantityWei = await (async () => {
    if ("quantityInWei" in options) {
      return options.quantityInWei;
    }
    const { toUnits: toUnits2 } = await import("./units-2VJP3HUQ.js");
    return toUnits2(options.quantity, await decimals({ contract: options.contract }));
  })();
  const [conditionId, { quantity, currency, pricePerToken, allowlistProof }] = await Promise.all([
    getActiveClaimConditionId({
      contract: options.contract
    }),
    getClaimParams({
      contract: options.contract,
      quantity: quantityWei,
      to: options.claimer,
      type: "erc20",
      from: options.from,
      tokenDecimals: await decimals({ contract: options.contract })
    })
  ]);
  try {
    await verifyClaim({
      contract: options.contract,
      claimer: options.claimer,
      quantity,
      currency,
      pricePerToken,
      allowlistProof,
      conditionId
    });
    return {
      result: true
    };
  } catch (error) {
    return {
      result: false,
      reason: await extractErrorResult({ error, contract: options.contract })
    };
  }
}

// node_modules/thirdweb/dist/esm/utils/extensions/drops/process-override-list.js
async function processOverrideList(options) {
  const hashEntryFn = options.hashEntry || hashEntry;
  const shardNybbles = options.shardNybbles || 2;
  const shards = {};
  for (const snapshotEntry of options.overrides) {
    const shard = snapshotEntry.address.slice(2, 2 + shardNybbles).toLowerCase();
    if (shards[shard] === void 0) {
      shards[shard] = [];
    }
    shards[shard].push(snapshotEntry);
  }
  const subTrees = await Promise.all(Object.entries(shards).map(async ([shard, entries]) => [
    shard,
    new MerkleTree(await Promise.all(entries.map(async (entry) => {
      return hashEntryFn({
        entry,
        chain: options.chain,
        client: options.client,
        tokenDecimals: options.tokenDecimals
      });
    }))).getHexRoot()
  ]));
  const roots = Object.fromEntries(subTrees);
  const tree = new MerkleTree(Object.values(roots));
  const shardsToUpload = [];
  for (const [shardId, entries] of Object.entries(shards)) {
    const data = {
      // biome-ignore lint/style/noNonNullAssertion: we know this is in bounds
      proofs: tree.getHexProof(roots[shardId]),
      entries
    };
    shardsToUpload.push({
      data: stringify(data),
      name: `${shardId}.json`
    });
  }
  let uris = await upload({
    client: options.client,
    files: shardsToUpload
  });
  if (!Array.isArray(uris)) {
    uris = [uris];
  }
  if (uris.length === 0) {
    throw new Error("No URIs returned from uploading merkle tree shards");
  }
  const baseUri = uris[0].slice(0, uris[0].lastIndexOf("/"));
  const originalEntriesUri = await upload({
    client: options.client,
    files: [stringify(options.overrides)]
  });
  const shardedMerkleInfo = {
    merkleRoot: tree.getHexRoot(),
    baseUri,
    originalEntriesUri,
    shardNybbles,
    tokenDecimals: options.tokenDecimals,
    isShardedMerkleTree: true
  };
  const finalUri = await upload({
    client: options.client,
    files: [shardedMerkleInfo]
  });
  return {
    shardedMerkleInfo,
    uri: finalUri
  };
}

// node_modules/thirdweb/dist/esm/utils/extensions/drops/get-multicall-set-claim-claim-conditon-transactions.js
async function getMulticallSetClaimConditionTransactions(options) {
  const merkleInfos = {};
  const phases = await Promise.all(options.phases.map(async (phase) => {
    var _a;
    let merkleRoot = phase.merkleRootHash || toHex("", { size: 32 });
    if (phase.overrideList) {
      const { shardedMerkleInfo, uri } = await processOverrideList({
        overrides: phase.overrideList,
        client: options.contract.client,
        chain: options.contract.chain,
        tokenDecimals: options.tokenDecimals
      });
      merkleInfos[shardedMerkleInfo.merkleRoot] = uri;
      merkleRoot = shardedMerkleInfo.merkleRoot;
    }
    let metadata = "";
    if (phase.metadata && typeof phase.metadata === "string") {
      metadata = phase.metadata;
    } else if (phase.metadata && typeof phase.metadata === "object") {
      metadata = await upload({
        client: options.contract.client,
        files: [phase.metadata]
      });
    }
    return {
      startTimestamp: dateToSeconds(phase.startTime ?? /* @__PURE__ */ new Date(0)),
      currency: phase.currencyAddress || NATIVE_TOKEN_ADDRESS,
      pricePerToken: await convertErc20Amount({
        chain: options.contract.chain,
        client: options.contract.client,
        erc20Address: phase.currencyAddress || NATIVE_TOKEN_ADDRESS,
        amount: ((_a = phase.price) == null ? void 0 : _a.toString()) ?? "0"
      }),
      maxClaimableSupply: phase.maxClaimableSupply ?? maxUint256,
      quantityLimitPerWallet: phase.maxClaimablePerWallet ?? maxUint256,
      merkleRoot,
      metadata,
      supplyClaimed: 0n
    };
  }));
  const encodedTransactions = [];
  if (Object.keys(merkleInfos).length > 0) {
    const [{ getContractMetadata }, { encodeSetContractURI }] = await Promise.all([
      import("./getContractMetadata-K7425VZF.js"),
      import("./setContractURI-3FJJYAS5.js")
    ]);
    const metadata = await getContractMetadata({
      contract: options.contract
    });
    for (const key of Object.keys(metadata.merkle || {})) {
      merkleInfos[key] = metadata.merkle[key];
    }
    const mergedMetadata = {
      ...metadata,
      merkle: merkleInfos
    };
    const uri = await upload({
      client: options.contract.client,
      files: [mergedMetadata]
    });
    const encodedSetContractURI = encodeSetContractURI({
      uri
    });
    encodedTransactions.push(encodedSetContractURI);
  }
  const sortedPhases = phases.sort((a, b) => Number(a.startTimestamp - b.startTimestamp));
  let encodedSetClaimConditions;
  if (options.tokenId !== void 0) {
    if (options.singlePhase) {
      const { encodeSetClaimConditions } = await import("./setClaimConditions-ZEJ5W3JL.js");
      const phase = sortedPhases[0];
      if (!phase) {
        throw new Error("No phase provided");
      }
      encodedSetClaimConditions = encodeSetClaimConditions({
        tokenId: options.tokenId,
        phase,
        resetClaimEligibility: options.resetClaimEligibility || false
      });
    } else {
      const { encodeSetClaimConditions } = await import("./setClaimConditions-3UWGTVXH.js");
      encodedSetClaimConditions = encodeSetClaimConditions({
        tokenId: options.tokenId,
        phases: sortedPhases,
        resetClaimEligibility: options.resetClaimEligibility || false
      });
    }
  } else {
    if (options.singlePhase) {
      const { encodeSetClaimConditions } = await import("./setClaimConditions-THU3342Y.js");
      const phase = sortedPhases[0];
      if (!phase) {
        throw new Error("No phase provided");
      }
      encodedSetClaimConditions = encodeSetClaimConditions({
        phase,
        resetClaimEligibility: options.resetClaimEligibility || false
      });
    } else {
      const { encodeSetClaimConditions } = await import("./setClaimConditions-3EV3HMKD.js");
      encodedSetClaimConditions = encodeSetClaimConditions({
        phases: sortedPhases,
        resetClaimEligibility: options.resetClaimEligibility || false
      });
    }
  }
  encodedTransactions.push(encodedSetClaimConditions);
  return encodedTransactions;
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IDropERC20/write/setClaimConditions.js
var FN_SELECTOR10 = "0x74bc7db7";
var FN_INPUTS10 = [
  {
    type: "tuple[]",
    name: "phases",
    components: [
      {
        type: "uint256",
        name: "startTimestamp"
      },
      {
        type: "uint256",
        name: "maxClaimableSupply"
      },
      {
        type: "uint256",
        name: "supplyClaimed"
      },
      {
        type: "uint256",
        name: "quantityLimitPerWallet"
      },
      {
        type: "bytes32",
        name: "merkleRoot"
      },
      {
        type: "uint256",
        name: "pricePerToken"
      },
      {
        type: "address",
        name: "currency"
      },
      {
        type: "string",
        name: "metadata"
      }
    ]
  },
  {
    type: "bool",
    name: "resetClaimEligibility"
  }
];
var FN_OUTPUTS10 = [];
function isSetClaimConditionsSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR10, FN_INPUTS10, FN_OUTPUTS10]
  });
}
function setClaimConditions(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR10, FN_INPUTS10, FN_OUTPUTS10],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [
        resolvedOptions.phases,
        resolvedOptions.resetClaimEligibility
      ];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/drops/write/setClaimConditions.js
function setClaimConditions2(options) {
  return multicall({
    contract: options.contract,
    asyncParams: async () => {
      return {
        data: await getMulticallSetClaimConditionTransactions({
          contract: options.contract,
          phases: options.phases,
          resetClaimEligibility: options.resetClaimEligibility,
          tokenDecimals: await decimals({ contract: options.contract }),
          singlePhase: options.singlePhaseDrop
        })
      };
    }
  });
}
function isSetClaimConditionsSupported2(availableSelectors) {
  return isMulticallSupported(availableSelectors) && // needed for setting contract metadata
  isContractURISupported(availableSelectors) && isSetContractURISupported(availableSelectors) && // needed for decimals
  isDecimalsSupported(availableSelectors) && // needs to actually be able to set the claim Conditions
  isSetClaimConditionsSupported(availableSelectors);
}

// node_modules/thirdweb/dist/esm/extensions/erc20/drops/write/resetClaimEligibility.js
function resetClaimEligibility(options) {
  return setClaimConditions({
    contract: options.contract,
    asyncParams: async () => {
      const existingConditions = await getClaimConditions(options);
      return {
        // type is necessary because of viem hex shenanigans (strict vs non-strict `0x` prefix string)
        phases: existingConditions,
        resetClaimEligibility: true
      };
    }
  });
}
function isResetClaimEligibilitySupported(availableSelectors) {
  return isGetClaimConditionsSupported(availableSelectors) && isSetClaimConditionsSupported(availableSelectors);
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/ISignatureMintERC20/write/mintWithSignature.js
var FN_SELECTOR11 = "0x8f0fefbb";
var FN_INPUTS11 = [
  {
    type: "tuple",
    name: "payload",
    components: [
      {
        type: "address",
        name: "to"
      },
      {
        type: "address",
        name: "primarySaleRecipient"
      },
      {
        type: "uint256",
        name: "quantity"
      },
      {
        type: "uint256",
        name: "price"
      },
      {
        type: "address",
        name: "currency"
      },
      {
        type: "uint128",
        name: "validityStartTimestamp"
      },
      {
        type: "uint128",
        name: "validityEndTimestamp"
      },
      {
        type: "bytes32",
        name: "uid"
      }
    ]
  },
  {
    type: "bytes",
    name: "signature"
  }
];
var FN_OUTPUTS11 = [];
function mintWithSignature(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR11, FN_INPUTS11, FN_OUTPUTS11],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.payload, resolvedOptions.signature];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/write/sigMint.js
function mintWithSignature2(options) {
  const value = isNativeTokenAddress(options.payload.currency) ? options.payload.price : 0n;
  const erc20Value = !isNativeTokenAddress(options.payload.currency) && options.payload.price > 0n ? {
    amountWei: options.payload.price,
    tokenAddress: options.payload.currency
  } : void 0;
  return mintWithSignature({
    ...options,
    overrides: {
      value,
      erc20Value
    }
  });
}
async function generateMintSignature(options) {
  const { mintRequest, account, contract } = options;
  const currency = mintRequest.currency || NATIVE_TOKEN_ADDRESS;
  const [price, quantity, uid, tokenName] = await Promise.all([
    // price per token in wei
    (async () => {
      if ("priceInWei" in mintRequest && mintRequest.priceInWei) {
        return mintRequest.priceInWei;
      }
      if ("price" in mintRequest && mintRequest.price) {
        const { convertErc20Amount: convertErc20Amount2 } = await import("./convert-erc20-amount-S4DNEMIG.js");
        return await convertErc20Amount2({
          amount: mintRequest.price,
          client: contract.client,
          chain: contract.chain,
          erc20Address: currency
        });
      }
      return 0n;
    })(),
    // quantity in wei
    (async () => {
      if ("quantityWei" in mintRequest) {
        return mintRequest.quantityWei;
      }
      const { convertErc20Amount: convertErc20Amount2 } = await import("./convert-erc20-amount-S4DNEMIG.js");
      return await convertErc20Amount2({
        amount: mintRequest.quantity,
        client: contract.client,
        chain: contract.chain,
        erc20Address: contract.address
      });
    })(),
    (() => {
      if (mintRequest.uid) {
        return isHex(mintRequest.uid) ? mintRequest.uid : stringToHex(mintRequest.uid, { size: 32 });
      }
      return randomBytesHex();
    })(),
    // ERC20Permit (EIP-712) spec differs from signature mint 721, 1155.
    // it uses the token name in the domain separator
    name({
      contract
    })
  ]);
  const startTime = mintRequest.validityStartTimestamp || /* @__PURE__ */ new Date(0);
  const endTime = mintRequest.validityEndTimestamp || tenYearsFromNow();
  const payload = {
    price,
    quantity,
    uid,
    currency,
    to: mintRequest.to,
    primarySaleRecipient: mintRequest.primarySaleRecipient || account.address,
    validityStartTimestamp: dateToSeconds(startTime),
    validityEndTimestamp: dateToSeconds(endTime)
  };
  const signature = await account.signTypedData({
    domain: {
      name: tokenName,
      version: "1",
      chainId: contract.chain.id,
      verifyingContract: contract.address
    },
    types: { MintRequest: MintRequest20 },
    primaryType: "MintRequest",
    message: payload
  });
  return { payload, signature };
}
var MintRequest20 = [
  { name: "to", type: "address" },
  { name: "primarySaleRecipient", type: "address" },
  { name: "quantity", type: "uint256" },
  { name: "price", type: "uint256" },
  { name: "currency", type: "address" },
  { name: "validityStartTimestamp", type: "uint128" },
  { name: "validityEndTimestamp", type: "uint128" },
  { name: "uid", type: "bytes32" }
];

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IWETH/write/deposit.js
var FN_SELECTOR12 = "0xd0e30db0";

// node_modules/thirdweb/dist/esm/extensions/erc20/write/deposit.js
function deposit(options) {
  const value = "amountWei" in options ? options.amountWei : toWei(options.amount);
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR12, [], []],
    value,
    erc20Value: {
      amountWei: value,
      tokenAddress: options.contract.address
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IWETH/write/withdraw.js
var FN_SELECTOR13 = "0x2e1a7d4d";
var FN_INPUTS12 = [
  {
    type: "uint256",
    name: "amount"
  }
];
var FN_OUTPUTS12 = [];
function withdraw(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR13, FN_INPUTS12, FN_OUTPUTS12],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.amount];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IERC20/events/Transfer.js
function transferEvent(filters = {}) {
  return prepareEvent({
    signature: "event Transfer(address indexed from, address indexed to, uint256 value)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IERC20/events/Approval.js
function approvalEvent(filters = {}) {
  return prepareEvent({
    signature: "event Approval(address indexed owner, address indexed spender, uint256 value)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IMintableERC20/events/TokensMinted.js
function tokensMintedEvent(filters = {}) {
  return prepareEvent({
    signature: "event TokensMinted(address indexed mintedTo, uint256 quantityMinted)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IDropERC20/events/TokensClaimed.js
function tokensClaimedEvent(filters = {}) {
  return prepareEvent({
    signature: "event TokensClaimed(uint256 indexed claimConditionIndex, address indexed claimer, address indexed receiver, uint256 quantityClaimed)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IDropERC20/events/ClaimConditionsUpdated.js
function claimConditionsUpdatedEvent() {
  return prepareEvent({
    signature: "event ClaimConditionsUpdated((uint256 startTimestamp, uint256 maxClaimableSupply, uint256 supplyClaimed, uint256 quantityLimitPerWallet, bytes32 merkleRoot, uint256 pricePerToken, address currency, string metadata)[] claimConditions, bool resetEligibility)"
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/ISignatureMintERC20/events/TokensMintedWithSignature.js
function tokensMintedWithSignatureEvent(filters = {}) {
  return prepareEvent({
    signature: "event TokensMintedWithSignature(address indexed signer, address indexed mintedTo, (address to, address primarySaleRecipient, uint256 quantity, uint256 price, address currency, uint128 validityStartTimestamp, uint128 validityEndTimestamp, bytes32 uid) mintRequest)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc20/__generated__/IVotes/write/delegate.js
var FN_SELECTOR14 = "0x5c19a95c";
var FN_INPUTS13 = [
  {
    type: "address",
    name: "delegatee"
  }
];
var FN_OUTPUTS13 = [];
function delegate(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR14, FN_INPUTS13, FN_OUTPUTS13],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.delegatee];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}
export {
  allowance,
  approvalEvent,
  approve,
  balanceOf,
  burn,
  burnFrom,
  canClaim,
  claimConditionsUpdatedEvent,
  claimTo,
  decimals,
  delegate,
  delegates,
  deposit,
  generateMintSignature,
  getActiveClaimCondition,
  getActiveClaimConditionId,
  getApprovalForTransaction,
  getBalance,
  getClaimConditionById,
  getClaimConditions,
  getCurrencyMetadata,
  isClaimToSupported,
  isERC20,
  isGetActiveClaimConditionIdSupported,
  isGetActiveClaimConditionSupported,
  isGetClaimConditionByIdSupported,
  isGetClaimConditionsSupported,
  isMintToSupported,
  isResetClaimEligibilitySupported,
  isSetClaimConditionsSupported2 as isSetClaimConditionsSupported,
  mintTo2 as mintTo,
  mintWithSignature2 as mintWithSignature,
  resetClaimEligibility,
  setClaimConditions2 as setClaimConditions,
  tokensClaimedEvent,
  tokensMintedEvent,
  tokensMintedWithSignatureEvent,
  totalSupply,
  transfer,
  transferBatch,
  transferEvent,
  transferFrom2 as transferFrom,
  withdraw
};
//# sourceMappingURL=thirdweb_extensions_erc20.js.map
