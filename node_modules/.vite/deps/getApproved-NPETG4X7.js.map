{"version": 3, "sources": ["../../thirdweb/src/extensions/erc721/__generated__/IERC721A/read/getApproved.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getApproved\" function.\n */\nexport type GetApprovedParams = {\n  tokenId: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"tokenId\" }>;\n};\n\nexport const FN_SELECTOR = \"0x081812fc\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"tokenId\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address\",\n  },\n] as const;\n\n/**\n * Checks if the `getApproved` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getApproved` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isGetApprovedSupported } from \"thirdweb/extensions/erc721\";\n * const supported = isGetApprovedSupported([\"0x...\"]);\n * ```\n */\nexport function isGetApprovedSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getApproved\" function.\n * @param options - The options for the getApproved function.\n * @returns The encoded ABI parameters.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeGetApprovedParams } from \"thirdweb/extensions/erc721\";\n * const result = encodeGetApprovedParams({\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeGetApprovedParams(options: GetApprovedParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.tokenId]);\n}\n\n/**\n * Encodes the \"getApproved\" function into a Hex string with its parameters.\n * @param options - The options for the getApproved function.\n * @returns The encoded hexadecimal string.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeGetApproved } from \"thirdweb/extensions/erc721\";\n * const result = encodeGetApproved({\n *  tokenId: ...,\n * });\n * ```\n */\nexport function encodeGetApproved(options: GetApprovedParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetApprovedParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getApproved function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC721\n * @example\n * ```ts\n * import { decodeGetApprovedResult } from \"thirdweb/extensions/erc721\";\n * const result = decodeGetApprovedResultResult(\"...\");\n * ```\n */\nexport function decodeGetApprovedResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getApproved\" function on the contract.\n * @param options - The options for the getApproved function.\n * @returns The parsed result of the function call.\n * @extension ERC721\n * @example\n * ```ts\n * import { getApproved } from \"thirdweb/extensions/erc721\";\n *\n * const result = await getApproved({\n *  contract,\n *  tokenId: ...,\n * });\n *\n * ```\n */\nexport async function getApproved(\n  options: BaseTransactionOptions<GetApprovedParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.tokenId],\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,uBAAuB,oBAA4B;AACjE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAeM,SAAU,wBAAwB,SAA0B;AAChE,SAAO,oBAAoB,WAAW,CAAC,QAAQ,OAAO,CAAC;AACzD;AAeM,SAAU,kBAAkB,SAA0B;AAG1D,SAAQ,cACN,wBAAwB,OAAO,EAAE,MAC/B,CAAC;AAEP;AAaM,SAAU,wBAAwB,QAAW;AACjD,SAAO,oBAAoB,YAAY,MAAM,EAAE,CAAC;AAClD;AAkBA,eAAsB,YACpB,SAAkD;AAElD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO;GACzB;AACH;", "names": []}