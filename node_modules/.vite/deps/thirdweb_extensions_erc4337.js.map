{"version": 3, "sources": ["../../thirdweb/src/extensions/erc4337/__generated__/IAccount/write/validateUserOp.ts", "../../thirdweb/src/extensions/erc4337/account/addAdmin.ts", "../../thirdweb/src/extensions/erc4337/account/removeAdmin.ts", "../../thirdweb/src/extensions/erc4337/account/removeSessionKey.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountFactory/read/getAddress.ts", "../../thirdweb/src/extensions/erc4337/account/isAccountDeployed.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountFactory/write/createAccount.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/read/getAllActiveSigners.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/read/getAllAdmins.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/read/getAllSigners.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/read/isActiveSigner.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/read/isAdmin.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/events/AdminUpdated.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/events/SignerPermissionsUpdated.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountFactory/read/getAllAccounts.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountFactory/read/getAccounts.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountFactory/read/totalAccounts.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountFactory/read/isRegistered.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountFactory/read/getAccountsOfSigner.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint/events/AccountDeployed.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint/events/UserOperationEvent.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint/write/simulateHandleOp.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"validateUserOp\" function.\n */\nexport type ValidateUserOpParams = WithOverrides<{\n  userOp: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"userOp\";\n    components: [\n      { type: \"address\"; name: \"sender\" },\n      { type: \"uint256\"; name: \"nonce\" },\n      { type: \"bytes\"; name: \"initCode\" },\n      { type: \"bytes\"; name: \"callData\" },\n      { type: \"uint256\"; name: \"callGasLimit\" },\n      { type: \"uint256\"; name: \"verificationGasLimit\" },\n      { type: \"uint256\"; name: \"preVerificationGas\" },\n      { type: \"uint256\"; name: \"maxFeePerGas\" },\n      { type: \"uint256\"; name: \"maxPriorityFeePerGas\" },\n      { type: \"bytes\"; name: \"paymasterAndData\" },\n      { type: \"bytes\"; name: \"signature\" },\n    ];\n  }>;\n  userOpHash: AbiParameterToPrimitiveType<{\n    type: \"bytes32\";\n    name: \"userOpHash\";\n  }>;\n  missingAccountFunds: AbiParameterToPrimitiveType<{\n    type: \"uint256\";\n    name: \"missingAccountFunds\";\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0x3a871cdd\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple\",\n    name: \"userOp\",\n    components: [\n      {\n        type: \"address\",\n        name: \"sender\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nonce\",\n      },\n      {\n        type: \"bytes\",\n        name: \"initCode\",\n      },\n      {\n        type: \"bytes\",\n        name: \"callData\",\n      },\n      {\n        type: \"uint256\",\n        name: \"callGasLimit\",\n      },\n      {\n        type: \"uint256\",\n        name: \"verificationGasLimit\",\n      },\n      {\n        type: \"uint256\",\n        name: \"preVerificationGas\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxFeePerGas\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxPriorityFeePerGas\",\n      },\n      {\n        type: \"bytes\",\n        name: \"paymasterAndData\",\n      },\n      {\n        type: \"bytes\",\n        name: \"signature\",\n      },\n    ],\n  },\n  {\n    type: \"bytes32\",\n    name: \"userOpHash\",\n  },\n  {\n    type: \"uint256\",\n    name: \"missingAccountFunds\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n    name: \"validationData\",\n  },\n] as const;\n\n/**\n * Checks if the `validateUserOp` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `validateUserOp` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isValidateUserOpSupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isValidateUserOpSupported([\"0x...\"]);\n * ```\n */\nexport function isValidateUserOpSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"validateUserOp\" function.\n * @param options - The options for the validateUserOp function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeValidateUserOpParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeValidateUserOpParams({\n *  userOp: ...,\n *  userOpHash: ...,\n *  missingAccountFunds: ...,\n * });\n * ```\n */\nexport function encodeValidateUserOpParams(options: ValidateUserOpParams) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.userOp,\n    options.userOpHash,\n    options.missingAccountFunds,\n  ]);\n}\n\n/**\n * Encodes the \"validateUserOp\" function into a Hex string with its parameters.\n * @param options - The options for the validateUserOp function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeValidateUserOp } from \"thirdweb/extensions/erc4337\";\n * const result = encodeValidateUserOp({\n *  userOp: ...,\n *  userOpHash: ...,\n *  missingAccountFunds: ...,\n * });\n * ```\n */\nexport function encodeValidateUserOp(options: ValidateUserOpParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeValidateUserOpParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"validateUserOp\" function on the contract.\n * @param options - The options for the \"validateUserOp\" function.\n * @returns A prepared transaction object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { validateUserOp } from \"thirdweb/extensions/erc4337\";\n *\n * const transaction = validateUserOp({\n *  contract,\n *  userOp: ...,\n *  userOpHash: ...,\n *  missingAccountFunds: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function validateUserOp(\n  options: BaseTransactionOptions<\n    | ValidateUserOpParams\n    | {\n        asyncParams: () => Promise<ValidateUserOpParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.userOp,\n        resolvedOptions.userOpHash,\n        resolvedOptions.missingAccountFunds,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport {\n  isSetPermissionsForSignerSupported,\n  setPermissionsForSigner,\n} from \"../__generated__/IAccountPermissions/write/setPermissionsForSigner.js\";\nimport { defaultPermissionsForAdmin, signPermissionRequest } from \"./common.js\";\n\n/**\n * @extension ERC4337\n */\nexport type AddAdminOptions = {\n  /**\n   * The admin account that will perform the operation.\n   */\n  account: Account;\n  /**\n   * The address to add as an admin.\n   */\n  adminAddress: string;\n};\n\n/**\n * Adds admin permissions for a specified address.\n * @param options - The options for the addAdmin function.\n * @returns The transaction object to be sent.\n * @example\n * ```ts\n * import { addAdmin } from 'thirdweb/extensions/erc4337';\n * import { sendTransaction } from 'thirdweb';\n *\n * const transaction = addAdmin({\n * contract,\n * account,\n * adminAddress: '0x...'\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n * @extension ERC4337\n */\nexport function addAdmin(options: BaseTransactionOptions<AddAdminOptions>) {\n  const { contract, account, adminAddress } = options;\n  return setPermissionsForSigner({\n    contract,\n    async asyncParams() {\n      const { req, signature } = await signPermissionRequest({\n        account,\n        contract,\n        req: await defaultPermissionsForAdmin({\n          target: adminAddress,\n          action: \"add-admin\",\n        }),\n      });\n      return { signature, req };\n    },\n  });\n}\n\n/**\n * Checks if the `isAddAdminSupported` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isAddAdminSupported` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isAddAdminSupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isAddAdminSupported([\"0x...\"]);\n * ```\n */\nexport function isAddAdminSupported(availableSelectors: string[]) {\n  return isSetPermissionsForSignerSupported(availableSelectors);\n}\n", "import type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport {\n  isSetPermissionsForSignerSupported,\n  setPermissionsForSigner,\n} from \"../__generated__/IAccountPermissions/write/setPermissionsForSigner.js\";\nimport { defaultPermissionsForAdmin, signPermissionRequest } from \"./common.js\";\n\n/**\n * @extension ERC4337\n */\nexport type RemoveAdminOptions = {\n  /**\n   * The admin account that will perform the operation.\n   */\n  account: Account;\n  /**\n   * The address to remove as an admin.\n   */\n  adminAddress: string;\n};\n\n/**\n * Removes admin permissions for a specified address.\n * @param options - The options for the removeAdmin function.\n * @returns The transaction object to be sent.\n * @example\n * ```ts\n * import { removeAdmin } from 'thirdweb/extensions/erc4337';\n * import { sendTransaction } from 'thirdweb';\n *\n * const transaction = removeAdmin({\n *  contract,\n *  account,\n *  adminAddress: '0x...'\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n * @extension ERC4337\n */\nexport function removeAdmin(\n  options: BaseTransactionOptions<RemoveAdminOptions>,\n) {\n  const { contract, account, adminAddress } = options;\n  return setPermissionsForSigner({\n    contract,\n    async asyncParams() {\n      const { req, signature } = await signPermissionRequest({\n        account,\n        contract,\n        req: await defaultPermissionsForAdmin({\n          target: adminAddress,\n          action: \"remove-admin\",\n        }),\n      });\n      return {\n        signature,\n        req,\n      };\n    },\n  });\n}\n\n/**\n * Checks if the `isRemoveAdminSupported` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isRemoveAdminSupported` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isRemoveAdminSupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isRemoveAdminSupported([\"0x...\"]);\n * ```\n */\nexport function isRemoveAdminSupported(availableSelectors: string[]) {\n  return isSetPermissionsForSignerSupported(availableSelectors);\n}\n", "import type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport {\n  isSetPermissionsForSignerSupported,\n  setPermissionsForSigner,\n} from \"../__generated__/IAccountPermissions/write/setPermissionsForSigner.js\";\nimport { signPermissionRequest, toContractPermissions } from \"./common.js\";\n\n/**\n * @extension ERC4337\n */\nexport type RemoveSessionKeyOptions = {\n  /**\n   * The account that will perform the operation.\n   */\n  account: Account;\n  /**\n   * The address to remove as a session key.\n   */\n  sessionKeyAddress: string;\n};\n\n/**\n * Removes session key permissions for a specified address.\n * @param options - The options for the removeSessionKey function.\n * @returns The transaction object to be sent.\n * @example\n * ```ts\n * import { removeSessionKey } from 'thirdweb/extensions/erc4337';\n * import { sendTransaction } from 'thirdweb';\n *\n * const transaction = removeSessionKey({\n * contract,\n * account,\n * sessionKeyAddress\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n * @extension ERC4337\n */\nexport function removeSessionKey(\n  options: BaseTransactionOptions<RemoveSessionKeyOptions>,\n) {\n  const { contract, account, sessionKeyAddress } = options;\n  return setPermissionsForSigner({\n    contract,\n    async asyncParams() {\n      const { req, signature } = await signPermissionRequest({\n        account,\n        contract,\n        req: await toContractPermissions({\n          target: sessionKeyAddress,\n          permissions: {\n            approvedTargets: [],\n            nativeTokenLimitPerTransaction: 0,\n            permissionStartTimestamp: new Date(0),\n            permissionEndTimestamp: new Date(0),\n          },\n        }),\n      });\n      return { signature, req };\n    },\n  });\n}\n\n/**\n * Checks if the `isRemoveSessionKeySupported` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isRemoveSessionKeySupported` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isRemoveSessionKeySupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isRemoveSessionKeySupported([\"0x...\"]);\n * ```\n */\nexport function isRemoveSessionKeySupported(availableSelectors: string[]) {\n  return isSetPermissionsForSignerSupported(availableSelectors);\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getAddress\" function.\n */\nexport type GetAddressParams = {\n  adminSigner: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"adminSigner\";\n  }>;\n  data: AbiParameterToPrimitiveType<{ type: \"bytes\"; name: \"data\" }>;\n};\n\nexport const FN_SELECTOR = \"0x8878ed33\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"adminSigner\",\n  },\n  {\n    type: \"bytes\",\n    name: \"data\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address\",\n  },\n] as const;\n\n/**\n * Checks if the `getAddress` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getAddress` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetAddressSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetAddressSupported([\"0x...\"]);\n * ```\n */\nexport function isGetAddressSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getAddress\" function.\n * @param options - The options for the getAddress function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetAddressParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetAddressParams({\n *  adminSigner: ...,\n *  data: ...,\n * });\n * ```\n */\nexport function encodeGetAddressParams(options: GetAddressParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.adminSigner, options.data]);\n}\n\n/**\n * Encodes the \"getAddress\" function into a Hex string with its parameters.\n * @param options - The options for the getAddress function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetAddress } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetAddress({\n *  adminSigner: ...,\n *  data: ...,\n * });\n * ```\n */\nexport function encodeGetAddress(options: GetAddressParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetAddressParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getAddress function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetAddressResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetAddressResultResult(\"...\");\n * ```\n */\nexport function decodeGetAddressResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getAddress\" function on the contract.\n * @param options - The options for the getAddress function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getAddress } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getAddress({\n *  contract,\n *  adminSigner: ...,\n *  data: ...,\n * });\n *\n * ```\n */\nexport async function getAddress(\n  options: BaseTransactionOptions<GetAddressParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.adminSigner, options.data],\n  });\n}\n", "import { getContract } from \"../../../contract/contract.js\";\nimport type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { isContractDeployed } from \"../../../utils/bytecode/is-contract-deployed.js\";\nimport * as PredictAddress from \"../__generated__/IAccountFactory/read/getAddress.js\";\n\nexport {\n  isGetAddressSupported as isIsAccountDeployedSupported,\n  type GetAddressParams as IsAccountDeployedParams,\n} from \"../__generated__/IAccountFactory/read/getAddress.js\";\n\n/**\n * Adds admin permissions for a specified address.\n * @param options - The options for the addAdmin function.\n * @returns The transaction object to be sent.\n * @example\n * ```ts\n * import { isAccountDeployed } from 'thirdweb/extensions/erc4337';\n *\n * const isDeployed = await isAccountDeployed({\n * contract,\n * account,\n * adminSigner: '0x...'\n * });\n *\n * await isAccountDeployed({ contract, adminSigner });\n * ```\n * @extension ERC4337\n */\nexport async function isAccountDeployed(\n  options: BaseTransactionOptions<PredictAddress.GetAddressParams>,\n): Promise<boolean> {\n  const predictedAddress = await PredictAddress.getAddress(options);\n  return isContractDeployed(\n    getContract({\n      ...options.contract,\n      address: predictedAddress,\n    }),\n  );\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"createAccount\" function.\n */\nexport type CreateAccountParams = WithOverrides<{\n  admin: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"admin\" }>;\n  data: AbiParameterToPrimitiveType<{ type: \"bytes\"; name: \"_data\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0xd8fd8f44\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"admin\",\n  },\n  {\n    type: \"bytes\",\n    name: \"_data\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address\",\n    name: \"account\",\n  },\n] as const;\n\n/**\n * Checks if the `createAccount` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `createAccount` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isCreateAccountSupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isCreateAccountSupported([\"0x...\"]);\n * ```\n */\nexport function isCreateAccountSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"createAccount\" function.\n * @param options - The options for the createAccount function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeCreateAccountParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeCreateAccountParams({\n *  admin: ...,\n *  data: ...,\n * });\n * ```\n */\nexport function encodeCreateAccountParams(options: CreateAccountParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.admin, options.data]);\n}\n\n/**\n * Encodes the \"createAccount\" function into a Hex string with its parameters.\n * @param options - The options for the createAccount function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeCreateAccount } from \"thirdweb/extensions/erc4337\";\n * const result = encodeCreateAccount({\n *  admin: ...,\n *  data: ...,\n * });\n * ```\n */\nexport function encodeCreateAccount(options: CreateAccountParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeCreateAccountParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"createAccount\" function on the contract.\n * @param options - The options for the \"createAccount\" function.\n * @returns A prepared transaction object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { createAccount } from \"thirdweb/extensions/erc4337\";\n *\n * const transaction = createAccount({\n *  contract,\n *  admin: ...,\n *  data: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function createAccount(\n  options: BaseTransactionOptions<\n    | CreateAccountParams\n    | {\n        asyncParams: () => Promise<CreateAccountParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.admin, resolvedOptions.data] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0x8b52d723\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"tuple[]\",\n    name: \"signers\",\n    components: [\n      {\n        type: \"address\",\n        name: \"signer\",\n      },\n      {\n        type: \"address[]\",\n        name: \"approvedTargets\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nativeTokenLimitPerTransaction\",\n      },\n      {\n        type: \"uint128\",\n        name: \"startTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"endTimestamp\",\n      },\n    ],\n  },\n] as const;\n\n/**\n * Checks if the `getAllActiveSigners` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getAllActiveSigners` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetAllActiveSignersSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetAllActiveSignersSupported([\"0x...\"]);\n * ```\n */\nexport function isGetAllActiveSignersSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the getAllActiveSigners function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetAllActiveSignersResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetAllActiveSignersResultResult(\"...\");\n * ```\n */\nexport function decodeGetAllActiveSignersResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getAllActiveSigners\" function on the contract.\n * @param options - The options for the getAllActiveSigners function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getAllActiveSigners } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getAllActiveSigners({\n *  contract,\n * });\n *\n * ```\n */\nexport async function getAllActiveSigners(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n", "import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0xe9523c97\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address[]\",\n    name: \"admins\",\n  },\n] as const;\n\n/**\n * Checks if the `getAllAdmins` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getAllAdmins` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetAllAdminsSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetAllAdminsSupported([\"0x...\"]);\n * ```\n */\nexport function isGetAllAdminsSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the getAllAdmins function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetAllAdminsResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetAllAdminsResultResult(\"...\");\n * ```\n */\nexport function decodeGetAllAdminsResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getAllAdmins\" function on the contract.\n * @param options - The options for the getAllAdmins function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getAllAdmins } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getAllAdmins({\n *  contract,\n * });\n *\n * ```\n */\nexport async function getAllAdmins(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n", "import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0xd42f2f35\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"tuple[]\",\n    name: \"signers\",\n    components: [\n      {\n        type: \"address\",\n        name: \"signer\",\n      },\n      {\n        type: \"address[]\",\n        name: \"approvedTargets\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nativeTokenLimitPerTransaction\",\n      },\n      {\n        type: \"uint128\",\n        name: \"startTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"endTimestamp\",\n      },\n    ],\n  },\n] as const;\n\n/**\n * Checks if the `getAllSigners` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getAllSigners` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetAllSignersSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetAllSignersSupported([\"0x...\"]);\n * ```\n */\nexport function isGetAllSignersSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the getAllSigners function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetAllSignersResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetAllSignersResultResult(\"...\");\n * ```\n */\nexport function decodeGetAllSignersResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getAllSigners\" function on the contract.\n * @param options - The options for the getAllSigners function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getAllSigners } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getAllSigners({\n *  contract,\n * });\n *\n * ```\n */\nexport async function getAllSigners(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"isActiveSigner\" function.\n */\nexport type IsActiveSignerParams = {\n  signer: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"signer\" }>;\n};\n\nexport const FN_SELECTOR = \"0x7dff5a79\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"signer\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bool\",\n  },\n] as const;\n\n/**\n * Checks if the `isActiveSigner` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isActiveSigner` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isIsActiveSignerSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isIsActiveSignerSupported([\"0x...\"]);\n * ```\n */\nexport function isIsActiveSignerSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"isActiveSigner\" function.\n * @param options - The options for the isActiveSigner function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeIsActiveSignerParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeIsActiveSignerParams({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeIsActiveSignerParams(options: IsActiveSignerParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.signer]);\n}\n\n/**\n * Encodes the \"isActiveSigner\" function into a Hex string with its parameters.\n * @param options - The options for the isActiveSigner function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeIsActiveSigner } from \"thirdweb/extensions/erc4337\";\n * const result = encodeIsActiveSigner({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeIsActiveSigner(options: IsActiveSignerParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeIsActiveSignerParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the isActiveSigner function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeIsActiveSignerResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeIsActiveSignerResultResult(\"...\");\n * ```\n */\nexport function decodeIsActiveSignerResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"isActiveSigner\" function on the contract.\n * @param options - The options for the isActiveSigner function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isActiveSigner } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await isActiveSigner({\n *  contract,\n *  signer: ...,\n * });\n *\n * ```\n */\nexport async function isActiveSigner(\n  options: BaseTransactionOptions<IsActiveSignerParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.signer],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"isAdmin\" function.\n */\nexport type IsAdminParams = {\n  signer: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"signer\" }>;\n};\n\nexport const FN_SELECTOR = \"0x24d7806c\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"signer\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bool\",\n  },\n] as const;\n\n/**\n * Checks if the `isAdmin` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isAdmin` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isIsAdminSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isIsAdminSupported([\"0x...\"]);\n * ```\n */\nexport function isIsAdminSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"isAdmin\" function.\n * @param options - The options for the isAdmin function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeIsAdminParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeIsAdminParams({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeIsAdminParams(options: IsAdminParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.signer]);\n}\n\n/**\n * Encodes the \"isAdmin\" function into a Hex string with its parameters.\n * @param options - The options for the isAdmin function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeIsAdmin } from \"thirdweb/extensions/erc4337\";\n * const result = encodeIsAdmin({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeIsAdmin(options: IsAdminParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeIsAdminParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the isAdmin function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeIsAdminResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeIsAdminResultResult(\"...\");\n * ```\n */\nexport function decodeIsAdminResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"isAdmin\" function on the contract.\n * @param options - The options for the isAdmin function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isAdmin } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await isAdmin({\n *  contract,\n *  signer: ...,\n * });\n *\n * ```\n */\nexport async function isAdmin(options: BaseTransactionOptions<IsAdminParams>) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.signer],\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"AdminUpdated\" event.\n */\nexport type AdminUpdatedEventFilters = Partial<{\n  signer: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"signer\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the AdminUpdated event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { adminUpdatedEvent } from \"thirdweb/extensions/erc4337\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  adminUpdatedEvent({\n *  signer: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function adminUpdatedEvent(filters: AdminUpdatedEventFilters = {}) {\n  return prepareEvent({\n    signature: \"event AdminUpdated(address indexed signer, bool isAdmin)\",\n    filters,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"SignerPermissionsUpdated\" event.\n */\nexport type SignerPermissionsUpdatedEventFilters = Partial<{\n  authorizingSigner: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"authorizingSigner\";\n    indexed: true;\n  }>;\n  targetSigner: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"targetSigner\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the SignerPermissionsUpdated event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { signerPermissionsUpdatedEvent } from \"thirdweb/extensions/erc4337\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  signerPermissionsUpdatedEvent({\n *  authorizingSigner: ...,\n *  targetSigner: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function signerPermissionsUpdatedEvent(\n  filters: SignerPermissionsUpdatedEventFilters = {},\n) {\n  return prepareEvent({\n    signature:\n      \"event SignerPermissionsUpdated(address indexed authorizingSigner, address indexed targetSigner, (address signer, uint8 isAdmin, address[] approvedTargets, uint256 nativeTokenLimitPerTransaction, uint128 permissionStartTimestamp, uint128 permissionEndTimestamp, uint128 reqValidityStartTimestamp, uint128 reqValidityEndTimestamp, bytes32 uid) permissions)\",\n    filters,\n  });\n}\n", "import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0x08e93d0a\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address[]\",\n  },\n] as const;\n\n/**\n * Checks if the `getAllAccounts` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getAllAccounts` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetAllAccountsSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetAllAccountsSupported([\"0x...\"]);\n * ```\n */\nexport function isGetAllAccountsSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the getAllAccounts function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetAllAccountsResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetAllAccountsResultResult(\"...\");\n * ```\n */\nexport function decodeGetAllAccountsResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getAllAccounts\" function on the contract.\n * @param options - The options for the getAllAccounts function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getAllAccounts } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getAllAccounts({\n *  contract,\n * });\n *\n * ```\n */\nexport async function getAllAccounts(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getAccounts\" function.\n */\nexport type GetAccountsParams = {\n  start: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"start\" }>;\n  end: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"end\" }>;\n};\n\nexport const FN_SELECTOR = \"0xe68a7c3b\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"start\",\n  },\n  {\n    type: \"uint256\",\n    name: \"end\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address[]\",\n  },\n] as const;\n\n/**\n * Checks if the `getAccounts` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getAccounts` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetAccountsSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetAccountsSupported([\"0x...\"]);\n * ```\n */\nexport function isGetAccountsSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getAccounts\" function.\n * @param options - The options for the getAccounts function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetAccountsParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetAccountsParams({\n *  start: ...,\n *  end: ...,\n * });\n * ```\n */\nexport function encodeGetAccountsParams(options: GetAccountsParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.start, options.end]);\n}\n\n/**\n * Encodes the \"getAccounts\" function into a Hex string with its parameters.\n * @param options - The options for the getAccounts function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetAccounts } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetAccounts({\n *  start: ...,\n *  end: ...,\n * });\n * ```\n */\nexport function encodeGetAccounts(options: GetAccountsParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetAccountsParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getAccounts function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetAccountsResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetAccountsResultResult(\"...\");\n * ```\n */\nexport function decodeGetAccountsResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getAccounts\" function on the contract.\n * @param options - The options for the getAccounts function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getAccounts } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getAccounts({\n *  contract,\n *  start: ...,\n *  end: ...,\n * });\n *\n * ```\n */\nexport async function getAccounts(\n  options: BaseTransactionOptions<GetAccountsParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.start, options.end],\n  });\n}\n", "import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0x58451f97\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n  },\n] as const;\n\n/**\n * Checks if the `totalAccounts` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `totalAccounts` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isTotalAccountsSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isTotalAccountsSupported([\"0x...\"]);\n * ```\n */\nexport function isTotalAccountsSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the totalAccounts function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeTotalAccountsResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeTotalAccountsResultResult(\"...\");\n * ```\n */\nexport function decodeTotalAccountsResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"totalAccounts\" function on the contract.\n * @param options - The options for the totalAccounts function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { totalAccounts } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await totalAccounts({\n *  contract,\n * });\n *\n * ```\n */\nexport async function totalAccounts(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"isRegistered\" function.\n */\nexport type IsRegisteredParams = {\n  account: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"account\" }>;\n};\n\nexport const FN_SELECTOR = \"0xc3c5a547\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"account\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bool\",\n  },\n] as const;\n\n/**\n * Checks if the `isRegistered` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isRegistered` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isIsRegisteredSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isIsRegisteredSupported([\"0x...\"]);\n * ```\n */\nexport function isIsRegisteredSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"isRegistered\" function.\n * @param options - The options for the isRegistered function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeIsRegisteredParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeIsRegisteredParams({\n *  account: ...,\n * });\n * ```\n */\nexport function encodeIsRegisteredParams(options: IsRegisteredParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.account]);\n}\n\n/**\n * Encodes the \"isRegistered\" function into a Hex string with its parameters.\n * @param options - The options for the isRegistered function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeIsRegistered } from \"thirdweb/extensions/erc4337\";\n * const result = encodeIsRegistered({\n *  account: ...,\n * });\n * ```\n */\nexport function encodeIsRegistered(options: IsRegisteredParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeIsRegisteredParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the isRegistered function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeIsRegisteredResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeIsRegisteredResultResult(\"...\");\n * ```\n */\nexport function decodeIsRegisteredResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"isRegistered\" function on the contract.\n * @param options - The options for the isRegistered function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isRegistered } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await isRegistered({\n *  contract,\n *  account: ...,\n * });\n *\n * ```\n */\nexport async function isRegistered(\n  options: BaseTransactionOptions<IsRegisteredParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.account],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getAccountsOfSigner\" function.\n */\nexport type GetAccountsOfSignerParams = {\n  signer: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"signer\" }>;\n};\n\nexport const FN_SELECTOR = \"0x0e6254fd\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"signer\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address[]\",\n    name: \"accounts\",\n  },\n] as const;\n\n/**\n * Checks if the `getAccountsOfSigner` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getAccountsOfSigner` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetAccountsOfSignerSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetAccountsOfSignerSupported([\"0x...\"]);\n * ```\n */\nexport function isGetAccountsOfSignerSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getAccountsOfSigner\" function.\n * @param options - The options for the getAccountsOfSigner function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetAccountsOfSignerParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetAccountsOfSignerParams({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeGetAccountsOfSignerParams(\n  options: GetAccountsOfSignerParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [options.signer]);\n}\n\n/**\n * Encodes the \"getAccountsOfSigner\" function into a Hex string with its parameters.\n * @param options - The options for the getAccountsOfSigner function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetAccountsOfSigner } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetAccountsOfSigner({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeGetAccountsOfSigner(options: GetAccountsOfSignerParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetAccountsOfSignerParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getAccountsOfSigner function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetAccountsOfSignerResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetAccountsOfSignerResultResult(\"...\");\n * ```\n */\nexport function decodeGetAccountsOfSignerResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getAccountsOfSigner\" function on the contract.\n * @param options - The options for the getAccountsOfSigner function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getAccountsOfSigner } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getAccountsOfSigner({\n *  contract,\n *  signer: ...,\n * });\n *\n * ```\n */\nexport async function getAccountsOfSigner(\n  options: BaseTransactionOptions<GetAccountsOfSignerParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.signer],\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"AccountDeployed\" event.\n */\nexport type AccountDeployedEventFilters = Partial<{\n  userOpHash: AbiParameterToPrimitiveType<{\n    type: \"bytes32\";\n    name: \"userOpHash\";\n    indexed: true;\n  }>;\n  sender: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"sender\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the AccountDeployed event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { accountDeployedEvent } from \"thirdweb/extensions/erc4337\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  accountDeployedEvent({\n *  userOpHash: ...,\n *  sender: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function accountDeployedEvent(\n  filters: AccountDeployedEventFilters = {},\n) {\n  return prepareEvent({\n    signature:\n      \"event AccountDeployed(bytes32 indexed userOpHash, address indexed sender, address factory, address paymaster)\",\n    filters,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"UserOperationEvent\" event.\n */\nexport type UserOperationEventEventFilters = Partial<{\n  userOpHash: AbiParameterToPrimitiveType<{\n    type: \"bytes32\";\n    name: \"userOpHash\";\n    indexed: true;\n  }>;\n  sender: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"sender\";\n    indexed: true;\n  }>;\n  paymaster: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"paymaster\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the UserOperationEvent event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { userOperationEventEvent } from \"thirdweb/extensions/erc4337\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  userOperationEventEvent({\n *  userOpHash: ...,\n *  sender: ...,\n *  paymaster: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function userOperationEventEvent(\n  filters: UserOperationEventEventFilters = {},\n) {\n  return prepareEvent({\n    signature:\n      \"event UserOperationEvent(bytes32 indexed userOpHash, address indexed sender, address indexed paymaster, uint256 nonce, bool success, uint256 actualGasCost, uint256 actualGasUsed)\",\n    filters,\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"simulateHandleOp\" function.\n */\nexport type SimulateHandleOpParams = WithOverrides<{\n  op: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"op\";\n    components: [\n      { type: \"address\"; name: \"sender\" },\n      { type: \"uint256\"; name: \"nonce\" },\n      { type: \"bytes\"; name: \"initCode\" },\n      { type: \"bytes\"; name: \"callData\" },\n      { type: \"uint256\"; name: \"callGasLimit\" },\n      { type: \"uint256\"; name: \"verificationGasLimit\" },\n      { type: \"uint256\"; name: \"preVerificationGas\" },\n      { type: \"uint256\"; name: \"maxFeePerGas\" },\n      { type: \"uint256\"; name: \"maxPriorityFeePerGas\" },\n      { type: \"bytes\"; name: \"paymasterAndData\" },\n      { type: \"bytes\"; name: \"signature\" },\n    ];\n  }>;\n  target: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"target\" }>;\n  targetCallData: AbiParameterToPrimitiveType<{\n    type: \"bytes\";\n    name: \"targetCallData\";\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0xd6383f94\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple\",\n    name: \"op\",\n    components: [\n      {\n        type: \"address\",\n        name: \"sender\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nonce\",\n      },\n      {\n        type: \"bytes\",\n        name: \"initCode\",\n      },\n      {\n        type: \"bytes\",\n        name: \"callData\",\n      },\n      {\n        type: \"uint256\",\n        name: \"callGasLimit\",\n      },\n      {\n        type: \"uint256\",\n        name: \"verificationGasLimit\",\n      },\n      {\n        type: \"uint256\",\n        name: \"preVerificationGas\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxFeePerGas\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxPriorityFeePerGas\",\n      },\n      {\n        type: \"bytes\",\n        name: \"paymasterAndData\",\n      },\n      {\n        type: \"bytes\",\n        name: \"signature\",\n      },\n    ],\n  },\n  {\n    type: \"address\",\n    name: \"target\",\n  },\n  {\n    type: \"bytes\",\n    name: \"targetCallData\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `simulateHandleOp` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `simulateHandleOp` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isSimulateHandleOpSupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isSimulateHandleOpSupported([\"0x...\"]);\n * ```\n */\nexport function isSimulateHandleOpSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"simulateHandleOp\" function.\n * @param options - The options for the simulateHandleOp function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeSimulateHandleOpParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeSimulateHandleOpParams({\n *  op: ...,\n *  target: ...,\n *  targetCallData: ...,\n * });\n * ```\n */\nexport function encodeSimulateHandleOpParams(options: SimulateHandleOpParams) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.op,\n    options.target,\n    options.targetCallData,\n  ]);\n}\n\n/**\n * Encodes the \"simulateHandleOp\" function into a Hex string with its parameters.\n * @param options - The options for the simulateHandleOp function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeSimulateHandleOp } from \"thirdweb/extensions/erc4337\";\n * const result = encodeSimulateHandleOp({\n *  op: ...,\n *  target: ...,\n *  targetCallData: ...,\n * });\n * ```\n */\nexport function encodeSimulateHandleOp(options: SimulateHandleOpParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeSimulateHandleOpParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"simulateHandleOp\" function on the contract.\n * @param options - The options for the \"simulateHandleOp\" function.\n * @returns A prepared transaction object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { simulateHandleOp } from \"thirdweb/extensions/erc4337\";\n *\n * const transaction = simulateHandleOp({\n *  contract,\n *  op: ...,\n *  target: ...,\n *  targetCallData: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function simulateHandleOp(\n  options: BaseTransactionOptions<\n    | SimulateHandleOpParams\n    | {\n        asyncParams: () => Promise<SimulateHandleOpParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.op,\n        resolvedOptions.target,\n        resolvedOptions.targetCallData,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AAgBJ,SAAU,0BAA0B,oBAA4B;AACpE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;;;ACrFM,SAAU,SAAS,SAAgD;AACvE,QAAM,EAAE,UAAU,SAAS,aAAY,IAAK;AAC5C,SAAO,wBAAwB;IAC7B;IACA,MAAM,cAAW;AACf,YAAM,EAAE,KAAK,UAAS,IAAK,MAAM,sBAAsB;QACrD;QACA;QACA,KAAK,MAAM,2BAA2B;UACpC,QAAQ;UACR,QAAQ;SACT;OACF;AACD,aAAO,EAAE,WAAW,IAAG;IACzB;GACD;AACH;AAcM,SAAU,oBAAoB,oBAA4B;AAC9D,SAAO,mCAAmC,kBAAkB;AAC9D;;;AChCM,SAAU,YACd,SAAmD;AAEnD,QAAM,EAAE,UAAU,SAAS,aAAY,IAAK;AAC5C,SAAO,wBAAwB;IAC7B;IACA,MAAM,cAAW;AACf,YAAM,EAAE,KAAK,UAAS,IAAK,MAAM,sBAAsB;QACrD;QACA;QACA,KAAK,MAAM,2BAA2B;UACpC,QAAQ;UACR,QAAQ;SACT;OACF;AACD,aAAO;QACL;QACA;;IAEJ;GACD;AACH;AAcM,SAAU,uBAAuB,oBAA4B;AACjE,SAAO,mCAAmC,kBAAkB;AAC9D;;;ACrCM,SAAU,iBACd,SAAwD;AAExD,QAAM,EAAE,UAAU,SAAS,kBAAiB,IAAK;AACjD,SAAO,wBAAwB;IAC7B;IACA,MAAM,cAAW;AACf,YAAM,EAAE,KAAK,UAAS,IAAK,MAAM,sBAAsB;QACrD;QACA;QACA,KAAK,MAAM,sBAAsB;UAC/B,QAAQ;UACR,aAAa;YACX,iBAAiB,CAAA;YACjB,gCAAgC;YAChC,0BAA0B,oBAAI,KAAK,CAAC;YACpC,wBAAwB,oBAAI,KAAK,CAAC;;SAErC;OACF;AACD,aAAO,EAAE,WAAW,IAAG;IACzB;GACD;AACH;AAcM,SAAU,4BAA4B,oBAA4B;AACtE,SAAO,mCAAmC,kBAAkB;AAC9D;;;AC7DO,IAAMA,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,sBAAsB,oBAA4B;AAChE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AA2EA,eAAsB,WACpB,SAAiD;AAEjD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAC,QAAQ,aAAa,QAAQ,IAAI;GAC3C;AACH;;;AC3GA,eAAsB,kBACpB,SAAgE;AAEhE,QAAM,mBAAmB,MAAqB,WAAW,OAAO;AAChE,SAAO,mBACL,YAAY;IACV,GAAG,QAAQ;IACX,SAAS;GACV,CAAC;AAEN;;;ACpBO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AAgBJ,SAAU,yBAAyB,oBAA4B;AACnE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAkEM,SAAU,cACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,OAAO,gBAAgB,IAAI;IACrD;IACA,OAAO,YAAS;AArIpB;AAqIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AAtIzB;AAsI6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAvIlB;AAuIsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAxIvB;AAwI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAzI3B;AAyI+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA1InC;AA2IO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AA5IpB;AA4IwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AA7IvB;AA6I2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA9IzB;AA8I6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA/IhC;AAgJO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AChJO,IAAMC,eAAc;AAC3B,IAAMC,aAAY,CAAA;AAClB,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAiBR,SAAU,+BAA+B,oBAA4B;AACzE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAgCA,eAAsB,oBAAoB,SAA+B;AACvE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAA;GACT;AACH;;;ACrFO,IAAMC,eAAc;AAC3B,IAAMC,aAAY,CAAA;AAClB,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AAeJ,SAAU,wBAAwB,oBAA4B;AAClE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAgCA,eAAsB,aAAa,SAA+B;AAChE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAA;GACT;AACH;;;AC/DO,IAAMC,eAAc;AAC3B,IAAMC,aAAY,CAAA;AAClB,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAiBR,SAAU,yBAAyB,oBAA4B;AACnE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAgCA,eAAsB,cAAc,SAA+B;AACjE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAA;GACT;AACH;;;AC7EO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,0BAA0B,oBAA4B;AACpE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAwEA,eAAsB,eACpB,SAAqD;AAErD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAC,QAAQ,MAAM;GACxB;AACH;;;AC7GO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,mBAAmB,oBAA4B;AAC7D,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAsEA,eAAsB,QAAQ,SAA8C;AAC1E,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAC,QAAQ,MAAM;GACxB;AACH;;;ACtFM,SAAU,kBAAkB,UAAoC,CAAA,GAAE;AACtE,SAAO,aAAa;IAClB,WAAW;IACX;GACD;AACH;;;ACCM,SAAU,8BACd,UAAgD,CAAA,GAAE;AAElD,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;ACzCO,IAAMC,eAAc;AAC3B,IAAMC,aAAY,CAAA;AAClB,IAAMC,cAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,0BAA0B,oBAA4B;AACpE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAgCA,eAAsB,eAAe,SAA+B;AAClE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAA;GACT;AACH;;;ACrDO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,uBAAuB,oBAA4B;AACjE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,eAAaC,aAAWC,YAAU;GAC5C;AACH;AA2EA,eAAsB,YACpB,SAAkD;AAElD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO,QAAQ,GAAG;GACpC;AACH;;;AC7HO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY,CAAA;AAClB,IAAMC,eAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,yBAAyB,oBAA4B;AACnE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,eAAaC,aAAWC,YAAU;GAC5C;AACH;AAgCA,eAAsB,cAAc,SAA+B;AACjE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,CAAA;GACT;AACH;;;ACtDO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa;EACjB;IACE,MAAM;;;AA4FV,eAAsB,aACpB,SAAmD;AAEnD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO;GACzB;AACH;;;AC7GO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AAeJ,SAAU,+BAA+B,oBAA4B;AACzE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,eAAaC,aAAWC,YAAU;GAC5C;AACH;AA0EA,eAAsB,oBACpB,SAA0D;AAE1D,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,CAAC,QAAQ,MAAM;GACxB;AACH;;;ACvFM,SAAU,qBACd,UAAuC,CAAA,GAAE;AAEzC,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;ACFM,SAAU,wBACd,UAA0C,CAAA,GAAE;AAE5C,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;AChBO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa,CAAA;AA4Fb,SAAU,iBACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO;QACL,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;;IAEpB;IACA,OAAO,YAAS;AAjNpB;AAiNwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AAlNzB;AAkN6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAnNlB;AAmNsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AApNvB;AAoN2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AArN3B;AAqN+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAtNnC;AAuNO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAxNpB;AAwNwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAzNvB;AAyN2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA1NzB;AA0N6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA3NhC;AA4NO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;", "names": ["FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS"]}