import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  readContract
} from "./chunk-A2MKHEYJ.js";
import {
  encodeAbiParameters
} from "./chunk-TVIYKLDL.js";
import "./chunk-JOYHE2MS.js";
import "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import {
  decodeAbiParameters
} from "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/erc721/__generated__/IERC721A/read/isApprovedForAll.js
var FN_SELECTOR = "0xe985e9c5";
var FN_INPUTS = [
  {
    type: "address",
    name: "owner"
  },
  {
    type: "address",
    name: "operator"
  }
];
var FN_OUTPUTS = [
  {
    type: "bool"
  }
];
function isIsApprovedForAllSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
function encodeIsApprovedForAllParams(options) {
  return encodeAbiParameters(FN_INPUTS, [options.owner, options.operator]);
}
function encodeIsApprovedForAll(options) {
  return FN_SELECTOR + encodeIsApprovedForAllParams(options).slice(2);
}
function decodeIsApprovedForAllResult(result) {
  return decodeAbiParameters(FN_OUTPUTS, result)[0];
}
async function isApprovedForAll(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: [options.owner, options.operator]
  });
}
export {
  FN_SELECTOR,
  decodeIsApprovedForAllResult,
  encodeIsApprovedForAll,
  encodeIsApprovedForAllParams,
  isApprovedForAll,
  isIsApprovedForAllSupported
};
//# sourceMappingURL=isApprovedForAll-IX2MEHFL.js.map
