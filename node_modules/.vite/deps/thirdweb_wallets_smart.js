import {
  smartWallet
} from "./chunk-JPM67O7D.js";
import "./chunk-HASQQNN5.js";
import {
  create7702MinimalAccount
} from "./chunk-PNPOQFQO.js";
import {
  bundleUserOp,
  createAndSignUserOp,
  createUnsignedUserOp,
  estimateUserOpGas,
  estimateUserOpGasCost,
  generateRandomUint192,
  getNonce,
  getPaymasterAndData,
  getUserOpGasFees,
  getUserOpHash,
  getUserOpReceipt,
  getUserOpReceiptRaw,
  getZkPaymasterData,
  prepareUserOp,
  signUserOp,
  waitForUserOpReceipt
} from "./chunk-UQKDXFU3.js";
import "./chunk-2YFWMLU6.js";
import "./chunk-URRAJUJZ.js";
import "./chunk-6IKHPL6Q.js";
import "./chunk-T4QBBWYN.js";
import "./chunk-HUQM4IHO.js";
import {
  hashMessage,
  hashTypedData,
  serializeErc6492Signature,
  verifyHash
} from "./chunk-DX2Q7WX7.js";
import "./chunk-NY3KIG23.js";
import "./chunk-ATOAWAR4.js";
import {
  encode as encode2,
  encodePacked
} from "./chunk-LJLZWTT7.js";
import {
  predictAddress,
  predictSmartAccountAddress
} from "./chunk-LPFMXKZP.js";
import {
  DEFAULT_ACCOUNT_FACTORY_V0_6,
  DEFAULT_ACCOUNT_FACTORY_V0_7,
  ENTRYPOINT_ADDRESS_v0_6,
  ENTRYPOINT_ADDRESS_v0_7,
  TokenPaymaster
} from "./chunk-AT6XHJ5F.js";
import "./chunk-XWXGM6C4.js";
import "./chunk-6HAUIGJE.js";
import "./chunk-CMXLKATA.js";
import "./chunk-LM2644TQ.js";
import "./chunk-S7XME4VF.js";
import "./chunk-DD6352NZ.js";
import "./chunk-Z2RZ5EOT.js";
import "./chunk-7HLZBJNZ.js";
import {
  encode
} from "./chunk-DVJU3TKU.js";
import "./chunk-DPFXQP4L.js";
import "./chunk-DUYIIKDP.js";
import "./chunk-26HKIFVU.js";
import "./chunk-EP7Z72A6.js";
import {
  once
} from "./chunk-NACC2RRT.js";
import "./chunk-JMHMJ42H.js";
import "./chunk-GLPFE2IH.js";
import "./chunk-DKMNAJQI.js";
import "./chunk-OVBFYP7T.js";
import {
  ZERO_ADDRESS
} from "./chunk-YCZ3YGMG.js";
import "./chunk-54TJVF2D.js";
import {
  prepareContractCall
} from "./chunk-Q44ZNVHQ.js";
import {
  readContract
} from "./chunk-A2MKHEYJ.js";
import {
  encodeAbiParameters
} from "./chunk-TVIYKLDL.js";
import "./chunk-JOYHE2MS.js";
import "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-QGXAPRFG.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import {
  getAddress
} from "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import {
  from2 as from,
  fromNumber2 as fromNumber,
  fromString2 as fromString,
  padRight,
  toBigInt2 as toBigInt,
  validate2 as validate
} from "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import {
  __export
} from "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/smart/presets/index.js
var presets_exports = {};
__export(presets_exports, {
  erc7579: () => erc7579
});

// node_modules/thirdweb/dist/esm/extensions/erc7579/__generated__/IERC7579Account/write/execute.js
var FN_SELECTOR = "0xe9ae5c53";
var FN_INPUTS = [
  {
    type: "bytes32",
    name: "mode"
  },
  {
    type: "bytes",
    name: "executionCalldata"
  }
];
var FN_OUTPUTS = [];
function execute(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.mode, resolvedOptions.executionCalldata];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc7579/__generated__/ModularAccountFactory/write/createAccountWithModules.js
var FN_SELECTOR2 = "0x7c37d0dc";
var FN_INPUTS2 = [
  {
    type: "address",
    name: "owner"
  },
  {
    type: "bytes",
    name: "salt"
  },
  {
    type: "tuple[]",
    name: "modules",
    components: [
      {
        type: "uint256",
        name: "moduleTypeId"
      },
      {
        type: "address",
        name: "module"
      },
      {
        type: "bytes",
        name: "initData"
      }
    ]
  }
];
var FN_OUTPUTS2 = [
  {
    type: "address"
  }
];
function createAccountWithModules(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR2, FN_INPUTS2, FN_OUTPUTS2],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [
        resolvedOptions.owner,
        resolvedOptions.salt,
        resolvedOptions.modules
      ];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/wallets/smart/presets/7579.js
function erc7579(options) {
  var _a, _b;
  const saltHex = ((_a = options.overrides) == null ? void 0 : _a.accountSalt) && validate(options.overrides.accountSalt) ? options.overrides.accountSalt : fromString(((_b = options.overrides) == null ? void 0 : _b.accountSalt) ?? "");
  const defaultValidator = getAddress(options.validatorAddress);
  const modularAccountOptions = {
    ...options,
    factoryAddress: options.factoryAddress,
    overrides: {
      entrypointAddress: ENTRYPOINT_ADDRESS_v0_7,
      createAccount(factoryContract, admin) {
        return createAccountWithModules({
          contract: factoryContract,
          asyncParams: async () => {
            const modules = [
              {
                moduleTypeId: 1n,
                // validator type id
                module: defaultValidator,
                initData: fromString("")
              }
            ];
            return {
              owner: admin,
              salt: saltHex,
              modules
            };
          }
        });
      },
      async predictAddress(factoryContract, admin) {
        return readContract({
          contract: factoryContract,
          method: "function getAddress(address owner, bytes salt) returns (address)",
          params: [admin, saltHex]
        });
      },
      execute(accountContract, transaction) {
        return execute({
          contract: accountContract,
          async asyncParams() {
            return {
              mode: padRight("0x00", 32),
              // single execution
              executionCalldata: encodePacked(["address", "uint256", "bytes"], [
                transaction.to || ZERO_ADDRESS,
                transaction.value || 0n,
                transaction.data || "0x"
              ])
            };
          }
        });
      },
      executeBatch(accountContract, transactions) {
        return execute({
          contract: accountContract,
          async asyncParams() {
            return {
              mode: padRight("0x01", 32),
              // batch execution
              executionCalldata: encode2([
                {
                  type: "tuple[]",
                  components: [
                    { type: "address", name: "to" },
                    { type: "uint256", name: "value" },
                    { type: "bytes", name: "data" }
                  ]
                }
              ], [
                transactions.map((t) => ({
                  to: t.to || ZERO_ADDRESS,
                  value: t.value || 0n,
                  data: t.data || "0x"
                }))
              ])
            };
          }
        });
      },
      async getAccountNonce(accountContract) {
        const entryPointNonce = await getNonce({
          contract: getContract({
            address: ENTRYPOINT_ADDRESS_v0_7,
            chain: accountContract.chain,
            client: accountContract.client
          }),
          key: generateRandomUint192(),
          sender: accountContract.address
        });
        const withValidator = from(`${defaultValidator}${fromNumber(entryPointNonce).slice(42)}`);
        return toBigInt(withValidator);
      },
      async signMessage(options2) {
        var _a2;
        const { accountContract, factoryContract, adminAccount, message } = options2;
        const originalMsgHash = hashMessage(message);
        const createAccount = (_a2 = modularAccountOptions.overrides) == null ? void 0 : _a2.createAccount;
        if (!createAccount) {
          throw new Error("Create account override not provided");
        }
        return generateSignature({
          accountContract,
          factoryContract,
          adminAccount,
          originalMsgHash,
          defaultValidator,
          createAccount
        });
      },
      async signTypedData(options2) {
        var _a2;
        const { accountContract, factoryContract, adminAccount, typedData } = options2;
        const originalMsgHash = hashTypedData(typedData);
        const createAccount = (_a2 = modularAccountOptions.overrides) == null ? void 0 : _a2.createAccount;
        if (!createAccount) {
          throw new Error("Create account override not provided");
        }
        return generateSignature({
          accountContract,
          factoryContract,
          adminAccount,
          originalMsgHash,
          defaultValidator,
          createAccount
        });
      },
      ...options.overrides
    }
  };
  return modularAccountOptions;
}
async function generateSignature(options) {
  const { accountContract, factoryContract, adminAccount, originalMsgHash, defaultValidator, createAccount } = options;
  const wrappedMessageHash = encodeAbiParameters([{ type: "bytes32" }], [originalMsgHash]);
  const rawSig = await adminAccount.signTypedData({
    domain: {
      // TODO (msa) - assumes our default validator here
      name: "DefaultValidator",
      version: "1",
      chainId: accountContract.chain.id,
      verifyingContract: defaultValidator
    },
    primaryType: "AccountMessage",
    types: { AccountMessage: [{ name: "message", type: "bytes" }] },
    message: { message: wrappedMessageHash }
  });
  const sig = encodeAbiParameters([{ type: "address" }, { type: "bytes" }], [defaultValidator, rawSig]);
  const deployTx = createAccount(factoryContract, adminAccount.address);
  if (!deployTx) {
    throw new Error("Create account override not provided");
  }
  const initCode = await encode(deployTx);
  const erc6492Sig = serializeErc6492Signature({
    address: factoryContract.address,
    data: initCode,
    signature: sig
  });
  const isValid = await verifyHash({
    hash: originalMsgHash,
    signature: erc6492Sig,
    address: accountContract.address,
    chain: accountContract.chain,
    client: accountContract.client
  });
  if (!isValid) {
    throw new Error(`Something went wrong generating the signature for modular smart account: ${accountContract.address} on chain ${accountContract.chain.id}`);
  }
  return erc6492Sig;
}
export {
  presets_exports as Config,
  DEFAULT_ACCOUNT_FACTORY_V0_6,
  DEFAULT_ACCOUNT_FACTORY_V0_7,
  ENTRYPOINT_ADDRESS_v0_6,
  ENTRYPOINT_ADDRESS_v0_7,
  TokenPaymaster,
  bundleUserOp,
  create7702MinimalAccount,
  createAndSignUserOp,
  createUnsignedUserOp,
  estimateUserOpGas,
  estimateUserOpGasCost,
  getPaymasterAndData,
  getUserOpGasFees,
  getUserOpHash,
  getUserOpReceipt,
  getUserOpReceiptRaw,
  getZkPaymasterData,
  predictAddress,
  predictSmartAccountAddress,
  prepareUserOp,
  signUserOp,
  smartWallet,
  waitForUserOpReceipt
};
//# sourceMappingURL=thirdweb_wallets_smart.js.map
