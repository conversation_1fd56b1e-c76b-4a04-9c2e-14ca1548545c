{"version": 3, "sources": ["../../thirdweb/src/extensions/erc1155/__generated__/IDropSinglePhase1155/write/setClaimConditions.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"setClaimConditions\" function.\n */\nexport type SetClaimConditionsParams = WithOverrides<{\n  tokenId: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"tokenId\" }>;\n  phase: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"phase\";\n    components: [\n      { type: \"uint256\"; name: \"startTimestamp\" },\n      { type: \"uint256\"; name: \"maxClaimableSupply\" },\n      { type: \"uint256\"; name: \"supplyClaimed\" },\n      { type: \"uint256\"; name: \"quantityLimitPerWallet\" },\n      { type: \"bytes32\"; name: \"merkleRoot\" },\n      { type: \"uint256\"; name: \"pricePerToken\" },\n      { type: \"address\"; name: \"currency\" },\n      { type: \"string\"; name: \"metadata\" },\n    ];\n  }>;\n  resetClaimEligibility: AbiParameterToPrimitiveType<{\n    type: \"bool\";\n    name: \"resetClaimEligibility\";\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0x8affb89f\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"tokenId\",\n  },\n  {\n    type: \"tuple\",\n    name: \"phase\",\n    components: [\n      {\n        type: \"uint256\",\n        name: \"startTimestamp\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxClaimableSupply\",\n      },\n      {\n        type: \"uint256\",\n        name: \"supplyClaimed\",\n      },\n      {\n        type: \"uint256\",\n        name: \"quantityLimitPerWallet\",\n      },\n      {\n        type: \"bytes32\",\n        name: \"merkleRoot\",\n      },\n      {\n        type: \"uint256\",\n        name: \"pricePerToken\",\n      },\n      {\n        type: \"address\",\n        name: \"currency\",\n      },\n      {\n        type: \"string\",\n        name: \"metadata\",\n      },\n    ],\n  },\n  {\n    type: \"bool\",\n    name: \"resetClaimEligibility\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `setClaimConditions` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `setClaimConditions` method is supported.\n * @extension ERC1155\n * @example\n * ```ts\n * import { isSetClaimConditionsSupported } from \"thirdweb/extensions/erc1155\";\n *\n * const supported = isSetClaimConditionsSupported([\"0x...\"]);\n * ```\n */\nexport function isSetClaimConditionsSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"setClaimConditions\" function.\n * @param options - The options for the setClaimConditions function.\n * @returns The encoded ABI parameters.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeSetClaimConditionsParams } from \"thirdweb/extensions/erc1155\";\n * const result = encodeSetClaimConditionsParams({\n *  tokenId: ...,\n *  phase: ...,\n *  resetClaimEligibility: ...,\n * });\n * ```\n */\nexport function encodeSetClaimConditionsParams(\n  options: SetClaimConditionsParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.tokenId,\n    options.phase,\n    options.resetClaimEligibility,\n  ]);\n}\n\n/**\n * Encodes the \"setClaimConditions\" function into a Hex string with its parameters.\n * @param options - The options for the setClaimConditions function.\n * @returns The encoded hexadecimal string.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeSetClaimConditions } from \"thirdweb/extensions/erc1155\";\n * const result = encodeSetClaimConditions({\n *  tokenId: ...,\n *  phase: ...,\n *  resetClaimEligibility: ...,\n * });\n * ```\n */\nexport function encodeSetClaimConditions(options: SetClaimConditionsParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeSetClaimConditionsParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"setClaimConditions\" function on the contract.\n * @param options - The options for the \"setClaimConditions\" function.\n * @returns A prepared transaction object.\n * @extension ERC1155\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { setClaimConditions } from \"thirdweb/extensions/erc1155\";\n *\n * const transaction = setClaimConditions({\n *  contract,\n *  tokenId: ...,\n *  phase: ...,\n *  resetClaimEligibility: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function setClaimConditions(\n  options: BaseTransactionOptions<\n    | SetClaimConditionsParams\n    | {\n        asyncParams: () => Promise<SetClaimConditionsParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.tokenId,\n        resolvedOptions.phase,\n        resolvedOptions.resetClaimEligibility,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa,CAAA;AAcb,SAAU,8BAA8B,oBAA4B;AACxE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAiBM,SAAU,+BACd,SAAiC;AAEjC,SAAO,oBAAoB,WAAW;IACpC,QAAQ;IACR,QAAQ;IACR,QAAQ;GACT;AACH;AAiBM,SAAU,yBAAyB,SAAiC;AAGxE,SAAQ,cACN,+BAA+B,OAAO,EAAE,MACtC,CAAC;AAEP;AA0BM,SAAU,mBACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO;QACL,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;;IAEpB;IACA,OAAO,YAAS;AApMpB;AAoMwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AArMzB;AAqM6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAtMlB;AAsMsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAvMvB;AAuM2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAxM3B;AAwM+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAzMnC;AA0MO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AA3MpB;AA2MwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AA5MvB;AA4M2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA7MzB;AA6M6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA9MhC;AA+MO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;", "names": []}