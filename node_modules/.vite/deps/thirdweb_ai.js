import {
  sendTransaction
} from "./chunk-S7XME4VF.js";
import "./chunk-DD6352NZ.js";
import "./chunk-Z2RZ5EOT.js";
import "./chunk-7HLZBJNZ.js";
import "./chunk-DVJU3TKU.js";
import "./chunk-DPFXQP4L.js";
import {
  toBigInt
} from "./chunk-JMHMJ42H.js";
import "./chunk-JOYHE2MS.js";
import "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-2CIJO3V3.js";
import {
  prepareTransaction
} from "./chunk-QGXAPRFG.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import {
  getCachedChain
} from "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import {
  getClientFetch
} from "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import {
  __export
} from "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/ai/index.js
var ai_exports = {};
__export(ai_exports, {
  chat: () => chat,
  execute: () => execute
});

// node_modules/thirdweb/dist/esm/ai/common.js
var NEBULA_API_URL = "https://nebula-api.thirdweb.com";
async function nebulaFetch(mode, input) {
  var _a;
  const fetch = getClientFetch(input.client);
  const response = await fetch(`${NEBULA_API_URL}/${mode}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      ..."messages" in input ? {
        messages: input.messages
      } : {
        message: input.message
      },
      session_id: input.sessionId,
      ...input.account ? {
        execute_config: {
          mode: "client",
          signer_wallet_address: input.account.address
        }
      } : {},
      ...input.contextFilter ? {
        context_filter: {
          chain_ids: ((_a = input.contextFilter.chains) == null ? void 0 : _a.map((c) => c.id.toString())) || [],
          wallet_addresses: input.contextFilter.walletAddresses || (input.account ? [input.account.address] : []),
          contract_addresses: input.contextFilter.contractAddresses || []
        }
      } : {}
    })
  });
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Nebula API error: ${error}`);
  }
  const data = await response.json();
  let transactions = [];
  if (data.actions) {
    transactions = data.actions.map((action) => {
      if (action.type === "sign_transaction") {
        const tx = JSON.parse(action.data);
        return prepareTransaction({
          chain: getCachedChain(tx.chainId),
          client: input.client,
          to: tx.to,
          value: tx.value ? toBigInt(tx.value) : void 0,
          data: tx.data
        });
      }
      return void 0;
    }).filter((tx) => tx !== void 0);
  }
  return {
    message: data.message,
    sessionId: data.session_id,
    transactions
  };
}

// node_modules/thirdweb/dist/esm/ai/chat.js
async function chat(input) {
  return nebulaFetch("chat", input);
}

// node_modules/thirdweb/dist/esm/ai/execute.js
async function execute(input) {
  const result = await nebulaFetch("execute", input);
  if (result.transactions.length === 0) {
    throw new Error(result.message);
  }
  const tx = result.transactions[0];
  if (!tx) {
    throw new Error(result.message);
  }
  return sendTransaction({
    transaction: tx,
    account: input.account
  });
}
export {
  ai_exports as Nebula
};
//# sourceMappingURL=thirdweb_ai.js.map
