import {
  eth_getStorageAt
} from "./chunk-O36RN4BR.js";
import {
  getBytecode
} from "./chunk-DKMNAJQI.js";
import {
  readContract
} from "./chunk-A2MKHEYJ.js";
import "./chunk-TVIYKLDL.js";
import "./chunk-JOYHE2MS.js";
import {
  getRpcClient
} from "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-2CIJO3V3.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import {
  isAddress
} from "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/utils/bytecode/extractMinimalProxyImplementationAddress.js
function extractMinimalProxyImplementationAddress(bytecode) {
  if (!bytecode.startsWith("0x")) {
    bytecode = `0x${bytecode}`;
  }
  if (bytecode.startsWith("0x363d3d373d3d3d363d73")) {
    const implementationAddress = bytecode.slice(22, 62);
    return `0x${implementationAddress}`;
  }
  if (bytecode.startsWith("0x36603057343d5230")) {
    const implementationAddress = bytecode.slice(122, 122 + 40);
    return `0x${implementationAddress}`;
  }
  if (bytecode.startsWith("0x3d3d3d3d363d3d37363d73")) {
    const implementationAddress = bytecode.slice(24, 24 + 40);
    return `0x${implementationAddress}`;
  }
  if (bytecode.startsWith("0x366000600037611000600036600073")) {
    const implementationAddress = bytecode.slice(32, 32 + 40);
    return `0x${implementationAddress}`;
  }
  if (bytecode.startsWith("0x36600080376020600036600073")) {
    const implementationAddress = bytecode.slice(28, 28 + 40);
    return `0x${implementationAddress}`;
  }
  if (bytecode.startsWith("0x365f5f375f5f365f73")) {
    const implementationAddress = bytecode.slice(20, 60);
    return `0x${implementationAddress}`;
  }
  if (bytecode.length === 48 && bytecode.startsWith("0xef0100")) {
    const implementationAddress = bytecode.slice(8, 48);
    return `0x${implementationAddress}`;
  }
  return void 0;
}

// node_modules/thirdweb/dist/esm/utils/bytecode/resolveImplementation.js
var AddressZero = "0x0000000000000000000000000000000000000000";
var ZERO_BYTES32 = "0x0000000000000000000000000000000000000000000000000000000000000000";
async function resolveImplementation(contract) {
  const [originalBytecode, beacon] = await Promise.all([
    getBytecode(contract),
    getBeaconFromStorageSlot(contract)
  ]);
  const minimalProxyImplementationAddress = extractMinimalProxyImplementationAddress(originalBytecode);
  if (minimalProxyImplementationAddress) {
    return {
      address: minimalProxyImplementationAddress,
      bytecode: await getBytecode(getContract({
        ...contract,
        address: minimalProxyImplementationAddress
      }))
    };
  }
  let implementationAddress;
  if (beacon && beacon !== AddressZero) {
    contract = getContract({
      ...contract,
      address: beacon
    });
    implementationAddress = await getImplementationFromContractCall(contract);
  } else {
    implementationAddress = await getImplementationFromStorageSlot(contract);
  }
  if (implementationAddress && isAddress(implementationAddress) && implementationAddress !== AddressZero) {
    const implementationBytecode = await getBytecode({
      ...contract,
      address: implementationAddress
    });
    if (implementationBytecode === "0x") {
      return {
        address: contract.address,
        bytecode: originalBytecode
      };
    }
    return {
      address: implementationAddress,
      bytecode: implementationBytecode
    };
  }
  return { address: contract.address, bytecode: originalBytecode };
}
async function getBeaconFromStorageSlot(contract) {
  const rpcRequest = getRpcClient({
    client: contract.client,
    chain: contract.chain
  });
  try {
    const proxyStorage = await eth_getStorageAt(rpcRequest, {
      address: contract.address,
      position: "0xa3f0ad74e5423aebfd80d3ef4346578335a9a72aeaee59ff6cb3582b35133d50"
    });
    return `0x${proxyStorage.slice(-40)}`;
  } catch {
    return void 0;
  }
}
async function getImplementationFromStorageSlot(contract) {
  const rpcRequest = getRpcClient({
    client: contract.client,
    chain: contract.chain
  });
  try {
    const proxyStoragePromises = [
      eth_getStorageAt(rpcRequest, {
        address: contract.address,
        position: "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc"
      }),
      eth_getStorageAt(rpcRequest, {
        address: contract.address,
        position: (
          // keccak256("matic.network.proxy.implementation") - used in polygon USDT proxy: https://polygonscan.com/address/******************************************#code
          "0xbaab7dbf64751104133af04abc7d9979f0fda3b059a322a8333f533d3f32bf7f"
        )
      }),
      eth_getStorageAt(rpcRequest, {
        address: contract.address,
        position: (
          // keccak256("org.zeppelinos.proxy.implementation") - e.g. base USDC proxy: https://basescan.org/address/******************************************#code
          "0x7050c9e0f4ca769c69bd3a8ef740bc37934f8e2c036e5a723fd8ee048ed3f8c3"
        )
      })
    ];
    const proxyStorages = await Promise.all(proxyStoragePromises);
    const proxyStorage = proxyStorages.find((storage) => storage !== ZERO_BYTES32);
    return proxyStorage ? `0x${proxyStorage.slice(-40)}` : AddressZero;
  } catch {
    return void 0;
  }
}
var UPGRADEABLE_PROXY_ABI = {
  type: "function",
  name: "implementation",
  inputs: [],
  outputs: [
    {
      type: "address",
      name: "",
      internalType: "address"
    }
  ],
  stateMutability: "view"
};
async function getImplementationFromContractCall(contract) {
  try {
    return await readContract({ contract, method: UPGRADEABLE_PROXY_ABI });
  } catch {
    return void 0;
  }
}
export {
  resolveImplementation
};
//# sourceMappingURL=resolveImplementation-76C6WDJ3.js.map
