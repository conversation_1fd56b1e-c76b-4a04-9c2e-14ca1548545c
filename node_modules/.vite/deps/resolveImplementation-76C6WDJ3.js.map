{"version": 3, "sources": ["../../thirdweb/src/utils/bytecode/extractMinimalProxyImplementationAddress.ts", "../../thirdweb/src/utils/bytecode/resolveImplementation.ts"], "sourcesContent": ["/**\n * Extracts the implementation address from a given bytecode string if it matches any of the known minimal proxy patterns.\n * @param bytecode The bytecode string to extract the implementation address from.\n * @returns The implementation address as a string if a match is found, otherwise undefined.\n * @example\n * ```ts\n * import { extractMinimalProxyImplementationAddress } from \"thirdweb/utils\";\n * const bytecode = \"0x363d3d373d3d3d363d73...\";\n * const implementationAddress = extractMinimalProxyImplementationAddress(bytecode);\n * ```\n * @utils\n */\nexport function extractMinimalProxyImplementationAddress(\n  bytecode: string,\n): string | undefined {\n  if (!bytecode.startsWith(\"0x\")) {\n    // biome-ignore lint/style/noParameterAssign: perf\n    bytecode = `0x${bytecode}`;\n  }\n  // EIP-1167 clone minimal proxy - https://eips.ethereum.org/EIPS/eip-1167\n  if (bytecode.startsWith(\"0x363d3d373d3d3d363d73\")) {\n    const implementationAddress = bytecode.slice(22, 62);\n    return `0x${implementationAddress}`;\n  }\n\n  // Minimal Proxy with receive() from 0xSplits - https://github.com/0xSplits/splits-contracts/blob/c7b741926ec9746182d0d1e2c4c2046102e5d337/contracts/libraries/Clones.sol\n  if (bytecode.startsWith(\"0x36603057343d5230\")) {\n    // +40 = size of addr\n    const implementationAddress = bytecode.slice(122, 122 + 40);\n    return `0x${implementationAddress}`;\n  }\n\n  // 0age's minimal proxy - https://medium.com/coinmonks/the-more-minimal-proxy-5756ae08ee48\n  if (bytecode.startsWith(\"0x3d3d3d3d363d3d37363d73\")) {\n    // +40 = size of addr\n    const implementationAddress = bytecode.slice(24, 24 + 40);\n    return `0x${implementationAddress}`;\n  }\n\n  // vyper's minimal proxy (uniswap v1) - https://etherscan.io/address/******************************************#code\n  if (bytecode.startsWith(\"0x366000600037611000600036600073\")) {\n    const implementationAddress = bytecode.slice(32, 32 + 40);\n    return `0x${implementationAddress}`;\n  }\n\n  if (bytecode.startsWith(\"0x36600080376020600036600073\")) {\n    const implementationAddress = bytecode.slice(28, 28 + 40);\n    return `0x${implementationAddress}`;\n  }\n\n  // EIP-7511 minimal proxy with PUSH0 opcode - https://eips.ethereum.org/EIPS/eip-7511\n  if (bytecode.startsWith(\"0x365f5f375f5f365f73\")) {\n    const implementationAddress = bytecode.slice(20, 60);\n    return `0x${implementationAddress}`;\n  }\n\n  // EIP-7702 - https://eips.ethereum.org/EIPS/eip-7702#abstract\n  if (bytecode.length === 48 && bytecode.startsWith(\"0xef0100\")) {\n    const implementationAddress = bytecode.slice(8, 48);\n    return `0x${implementationAddress}`;\n  }\n\n  return undefined;\n}\n", "import { getBytecode } from \"../../contract/actions/get-bytecode.js\";\nimport { type ThirdwebContract, getContract } from \"../../contract/contract.js\";\nimport { eth_getStorageAt } from \"../../rpc/actions/eth_getStorageAt.js\";\nimport { getRpcClient } from \"../../rpc/rpc.js\";\nimport { readContract } from \"../../transaction/read-contract.js\";\nimport { isAddress } from \"../address.js\";\nimport type { Hex } from \"../encoding/hex.js\";\nimport { extractMinimalProxyImplementationAddress } from \"./extractMinimalProxyImplementationAddress.js\";\n\n// TODO: move to const exports\nconst AddressZero = \"******************************************\";\nconst ZERO_BYTES32 =\n  \"******************************************000000000000000000000000\";\n\n/**\n * Resolves the implementation address and bytecode for a given proxy contract.\n * @param contract The contract to resolve the implementation for.\n * @returns A promise that resolves to an object containing the implementation address and bytecode.\n * @example\n * ```ts\n * import { resolveImplementation } from \"thirdweb\";\n * const implementation = await resolveImplementation(contract);\n * ```\n * @contract\n */\nexport async function resolveImplementation(\n  // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n  contract: ThirdwebContract<any>,\n): Promise<{ address: string; bytecode: Hex }> {\n  const [originalBytecode, beacon] = await Promise.all([\n    getBytecode(contract),\n    getBeaconFromStorageSlot(contract),\n  ]);\n  // check minimal proxy first synchronously\n  const minimalProxyImplementationAddress =\n    extractMinimalProxyImplementationAddress(originalBytecode);\n  if (minimalProxyImplementationAddress) {\n    return {\n      address: minimalProxyImplementationAddress,\n      bytecode: await getBytecode(\n        getContract({\n          ...contract,\n          address: minimalProxyImplementationAddress,\n        }),\n      ),\n    };\n  }\n\n  // check other proxy types\n  let implementationAddress: string | undefined;\n\n  if (beacon && beacon !== AddressZero) {\n    // In case of a BeaconProxy, it is setup as BeaconProxy --> Beacon --> Implementation\n    // Hence we replace the proxy address with Beacon address, and continue further resolving below\n    // biome-ignore lint/style/noParameterAssign: we purposefully mutate the contract object here\n    contract = getContract({\n      ...contract,\n      address: beacon,\n    });\n\n    implementationAddress = await getImplementationFromContractCall(contract);\n  } else {\n    implementationAddress = await getImplementationFromStorageSlot(contract);\n  }\n\n  if (\n    implementationAddress &&\n    isAddress(implementationAddress) &&\n    implementationAddress !== AddressZero\n  ) {\n    const implementationBytecode = await getBytecode({\n      ...contract,\n      address: implementationAddress,\n    });\n    // return the original contract bytecode if the implementation bytecode is empty\n    if (implementationBytecode === \"0x\") {\n      return {\n        address: contract.address,\n        bytecode: originalBytecode,\n      };\n    }\n\n    return {\n      address: implementationAddress,\n      bytecode: implementationBytecode,\n    };\n  }\n\n  return { address: contract.address, bytecode: originalBytecode };\n}\n\nasync function getBeaconFromStorageSlot(\n  // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n  contract: ThirdwebContract<any>,\n): Promise<string | undefined> {\n  /**\n   * The storage slot of the Beacon as defined in EIP-1967\n   * See https://eips.ethereum.org/EIPS/eip-1967#beacon-contract-address\n   *\n   * bytes32(uint256(keccak256('eip1967.proxy.beacon')) - 1))\n   */\n  const rpcRequest = getRpcClient({\n    client: contract.client,\n    chain: contract.chain,\n  });\n\n  try {\n    const proxyStorage = await eth_getStorageAt(rpcRequest, {\n      address: contract.address,\n      position:\n        \"0xa3f0ad74e5423aebfd80d3ef4346578335a9a72aeaee59ff6cb3582b35133d50\",\n    });\n    return `0x${proxyStorage.slice(-40)}`;\n  } catch {\n    return undefined;\n  }\n}\n\nasync function getImplementationFromStorageSlot(\n  // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n  contract: ThirdwebContract<any>,\n): Promise<string | undefined> {\n  const rpcRequest = getRpcClient({\n    client: contract.client,\n    chain: contract.chain,\n  });\n\n  try {\n    const proxyStoragePromises = [\n      eth_getStorageAt(rpcRequest, {\n        address: contract.address,\n        position:\n          \"0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc\",\n      }),\n      eth_getStorageAt(rpcRequest, {\n        address: contract.address,\n        position:\n          // keccak256(\"matic.network.proxy.implementation\") - used in polygon USDT proxy: https://polygonscan.com/address/******************************************#code\n          \"0xbaab7dbf64751104133af04abc7d9979f0fda3b059a322a8333f533d3f32bf7f\",\n      }),\n      eth_getStorageAt(rpcRequest, {\n        address: contract.address,\n        position:\n          // keccak256(\"org.zeppelinos.proxy.implementation\") - e.g. base USDC proxy: https://basescan.org/address/******************************************#code\n          \"0x7050c9e0f4ca769c69bd3a8ef740bc37934f8e2c036e5a723fd8ee048ed3f8c3\",\n      }),\n    ];\n\n    const proxyStorages = await Promise.all(proxyStoragePromises);\n    const proxyStorage = proxyStorages.find(\n      (storage) => storage !== ZERO_BYTES32,\n    );\n\n    return proxyStorage ? `0x${proxyStorage.slice(-40)}` : AddressZero;\n  } catch {\n    return undefined;\n  }\n}\n\nconst UPGRADEABLE_PROXY_ABI = {\n  type: \"function\",\n  name: \"implementation\",\n  inputs: [],\n  outputs: [\n    {\n      type: \"address\",\n      name: \"\",\n      internalType: \"address\",\n    },\n  ],\n  stateMutability: \"view\",\n} as const;\n\nasync function getImplementationFromContractCall(\n  // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n  contract: ThirdwebContract<any>,\n): Promise<string | undefined> {\n  try {\n    return await readContract({ contract, method: UPGRADEABLE_PROXY_ABI });\n  } catch {\n    return undefined;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYM,SAAU,yCACd,UAAgB;AAEhB,MAAI,CAAC,SAAS,WAAW,IAAI,GAAG;AAE9B,eAAW,KAAK,QAAQ;EAC1B;AAEA,MAAI,SAAS,WAAW,wBAAwB,GAAG;AACjD,UAAM,wBAAwB,SAAS,MAAM,IAAI,EAAE;AACnD,WAAO,KAAK,qBAAqB;EACnC;AAGA,MAAI,SAAS,WAAW,oBAAoB,GAAG;AAE7C,UAAM,wBAAwB,SAAS,MAAM,KAAK,MAAM,EAAE;AAC1D,WAAO,KAAK,qBAAqB;EACnC;AAGA,MAAI,SAAS,WAAW,0BAA0B,GAAG;AAEnD,UAAM,wBAAwB,SAAS,MAAM,IAAI,KAAK,EAAE;AACxD,WAAO,KAAK,qBAAqB;EACnC;AAGA,MAAI,SAAS,WAAW,kCAAkC,GAAG;AAC3D,UAAM,wBAAwB,SAAS,MAAM,IAAI,KAAK,EAAE;AACxD,WAAO,KAAK,qBAAqB;EACnC;AAEA,MAAI,SAAS,WAAW,8BAA8B,GAAG;AACvD,UAAM,wBAAwB,SAAS,MAAM,IAAI,KAAK,EAAE;AACxD,WAAO,KAAK,qBAAqB;EACnC;AAGA,MAAI,SAAS,WAAW,sBAAsB,GAAG;AAC/C,UAAM,wBAAwB,SAAS,MAAM,IAAI,EAAE;AACnD,WAAO,KAAK,qBAAqB;EACnC;AAGA,MAAI,SAAS,WAAW,MAAM,SAAS,WAAW,UAAU,GAAG;AAC7D,UAAM,wBAAwB,SAAS,MAAM,GAAG,EAAE;AAClD,WAAO,KAAK,qBAAqB;EACnC;AAEA,SAAO;AACT;;;ACrDA,IAAM,cAAc;AACpB,IAAM,eACJ;AAaF,eAAsB,sBAEpB,UAA+B;AAE/B,QAAM,CAAC,kBAAkB,MAAM,IAAI,MAAM,QAAQ,IAAI;IACnD,YAAY,QAAQ;IACpB,yBAAyB,QAAQ;GAClC;AAED,QAAM,oCACJ,yCAAyC,gBAAgB;AAC3D,MAAI,mCAAmC;AACrC,WAAO;MACL,SAAS;MACT,UAAU,MAAM,YACd,YAAY;QACV,GAAG;QACH,SAAS;OACV,CAAC;;EAGR;AAGA,MAAI;AAEJ,MAAI,UAAU,WAAW,aAAa;AAIpC,eAAW,YAAY;MACrB,GAAG;MACH,SAAS;KACV;AAED,4BAAwB,MAAM,kCAAkC,QAAQ;EAC1E,OAAO;AACL,4BAAwB,MAAM,iCAAiC,QAAQ;EACzE;AAEA,MACE,yBACA,UAAU,qBAAqB,KAC/B,0BAA0B,aAC1B;AACA,UAAM,yBAAyB,MAAM,YAAY;MAC/C,GAAG;MACH,SAAS;KACV;AAED,QAAI,2BAA2B,MAAM;AACnC,aAAO;QACL,SAAS,SAAS;QAClB,UAAU;;IAEd;AAEA,WAAO;MACL,SAAS;MACT,UAAU;;EAEd;AAEA,SAAO,EAAE,SAAS,SAAS,SAAS,UAAU,iBAAgB;AAChE;AAEA,eAAe,yBAEb,UAA+B;AAQ/B,QAAM,aAAa,aAAa;IAC9B,QAAQ,SAAS;IACjB,OAAO,SAAS;GACjB;AAED,MAAI;AACF,UAAM,eAAe,MAAM,iBAAiB,YAAY;MACtD,SAAS,SAAS;MAClB,UACE;KACH;AACD,WAAO,KAAK,aAAa,MAAM,GAAG,CAAC;EACrC,QAAQ;AACN,WAAO;EACT;AACF;AAEA,eAAe,iCAEb,UAA+B;AAE/B,QAAM,aAAa,aAAa;IAC9B,QAAQ,SAAS;IACjB,OAAO,SAAS;GACjB;AAED,MAAI;AACF,UAAM,uBAAuB;MAC3B,iBAAiB,YAAY;QAC3B,SAAS,SAAS;QAClB,UACE;OACH;MACD,iBAAiB,YAAY;QAC3B,SAAS,SAAS;QAClB;;UAEE;;OACH;MACD,iBAAiB,YAAY;QAC3B,SAAS,SAAS;QAClB;;UAEE;;OACH;;AAGH,UAAM,gBAAgB,MAAM,QAAQ,IAAI,oBAAoB;AAC5D,UAAM,eAAe,cAAc,KACjC,CAAC,YAAY,YAAY,YAAY;AAGvC,WAAO,eAAe,KAAK,aAAa,MAAM,GAAG,CAAC,KAAK;EACzD,QAAQ;AACN,WAAO;EACT;AACF;AAEA,IAAM,wBAAwB;EAC5B,MAAM;EACN,MAAM;EACN,QAAQ,CAAA;EACR,SAAS;IACP;MACE,MAAM;MACN,MAAM;MACN,cAAc;;;EAGlB,iBAAiB;;AAGnB,eAAe,kCAEb,UAA+B;AAE/B,MAAI;AACF,WAAO,MAAM,aAAa,EAAE,UAAU,QAAQ,sBAAqB,CAAE;EACvE,QAAQ;AACN,WAAO;EACT;AACF;", "names": []}