import {
  polygon
} from "./chunk-CP5Y63S2.js";
import {
  ethereum,
  mainnet
} from "./chunk-NHUN2R7O.js";
import {
  base,
  baseSepolia,
  optimism,
  optimismS<PERSON><PERSON>,
  zora,
  zoraSepolia
} from "./chunk-BJ2DNF5Z.js";
import {
  define<PERSON>hain,
  getChainMetadata,
  getRpcUrl<PERSON>or<PERSON>hain
} from "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/chains/chain-definitions/anvil.js
var anvil = defineChain({
  id: 31337,
  name: "Anvil",
  rpc: "http://127.0.0.1:8545",
  testnet: true,
  nativeCurrency: {
    name: "Anvil Ether",
    symbol: "ETH",
    decimals: 18
  }
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/hardhat.js
var hardhat = defineChain({
  id: 31337,
  name: "Hardhat",
  rpc: "http://127.0.0.1:8545",
  testnet: true,
  nativeCurrency: {
    name: "Hardhat Ether",
    symbol: "ETH",
    decimals: 18
  }
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/arbitrum-nova.js
var arbitrumNova = defineChain({
  id: 42170,
  name: "Arbitrum Nova",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Arbiscan",
      url: "https://nova.arbiscan.io/"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/arbitrum-sepolia.js
var arbitrumSepolia = defineChain({
  id: 421614,
  name: "Arbitrum Sepolia",
  nativeCurrency: {
    name: "Arbitrum Sepolia Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Arbiscan",
      url: "https://sepolia.arbiscan.io",
      apiUrl: "https://sepolia.arbiscan.io/api"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/arbitrum.js
var arbitrum = defineChain({
  id: 42161,
  name: "Arbitrum One",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Arbiscan",
      url: "https://arbiscan.io",
      apiUrl: "https://api.arbiscan.io/api"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/avalanche-fuji.js
var avalancheFuji = defineChain({
  id: 43113,
  name: "Avalanche Fuji",
  nativeCurrency: {
    decimals: 18,
    name: "Avalanche Fuji",
    symbol: "AVAX"
  },
  blockExplorers: [
    {
      name: "SnowTrace",
      url: "https://testnet.snowtrace.io",
      apiUrl: "https://api-testnet.snowtrace.io/api"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/avalanche.js
var avalanche = defineChain({
  id: 43114,
  name: "Avalanche",
  nativeCurrency: {
    decimals: 18,
    name: "Avalanche",
    symbol: "AVAX"
  },
  blockExplorers: [
    {
      name: "SnowTrace",
      url: "https://snowtrace.io",
      apiUrl: "https://api.snowtrace.io/api"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/blast.js
var blast = defineChain({
  id: 81457,
  name: "Blast",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Blastscan",
      url: "https://blastscan.io",
      apiUrl: "https://api.blastscan.io/api"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/linea-sepolia.js
var lineaSepolia = defineChain({
  id: 59141,
  name: "Linea Sepolia",
  nativeCurrency: { name: "Sepolia Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "LineaScan",
      url: "https://sepolia.lineascan.build",
      apiUrl: "https://api-sepolia.lineascan.build/api"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/linea.js
var linea = defineChain({
  id: 59144,
  name: "Linea",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "LineaScan",
      url: "https://lineascan.build",
      apiUrl: "https://api.lineascan.build/api"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/astria-evm-dusknet.js
var astriaEvmDusknet = defineChain({
  id: 912559,
  name: "Astria EVM Dusknet",
  nativeCurrency: { name: "RIA", symbol: "RIA", decimals: 18 },
  blockExplorers: [
    {
      name: "Astria EVM Dusknet Explorer",
      url: "https://explorer.evm.dusk-3.devnet.astria.org/"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/fantom.js
var fantom = defineChain({
  id: 250,
  name: "Fantom Opera",
  nativeCurrency: {
    name: "Fantom",
    symbol: "FTM",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "ftmscan",
      url: "https://ftmscan.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/polygon-zkevm.js
var polygonZkEvm = defineChain({
  id: 1101,
  name: "Polygon zkEVM",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "blockscout",
      url: "https://zkevm.polygonscan.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/gnosis.js
var gnosis = defineChain({
  id: 100,
  name: "Gnosis",
  nativeCurrency: { name: "xDAI", symbol: "XDAI", decimals: 18 },
  blockExplorers: [
    {
      name: "blockscout",
      url: "https://gnosis.blockscout.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/manta-pacific.js
var mantaPacific = defineChain({
  id: 169,
  name: "Manta Pacific Mainnet",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "manta-pacific Explorer",
      url: "https://pacific-explorer.manta.network"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/xai.js
var xai = defineChain({
  id: 660279,
  name: "Xai Mainnet",
  nativeCurrency: {
    decimals: 18,
    name: "XAI token",
    symbol: "XAI"
  },
  blockExplorers: [
    {
      name: "Blockscout",
      url: "https://explorer.xai-chain.net"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/celo.js
var celo = defineChain({
  id: 42220,
  name: "Celo Mainnet",
  nativeCurrency: {
    name: "CELO",
    symbol: "CELO",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "blockscout",
      url: "https://explorer.celo.org"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/cronos.js
var cronos = defineChain({
  id: 25,
  name: "Cronos Mainnet",
  nativeCurrency: {
    name: "Cronos",
    symbol: "CRO",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Cronos Explorer",
      url: "https://explorer.cronos.org"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/degen.js
var degen = defineChain({
  id: 666666666,
  name: "Degen Chain",
  nativeCurrency: {
    name: "DEGEN",
    symbol: "DEGEN",
    decimals: 18
  },
  blockExplorers: []
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/scroll.js
var scroll = defineChain({
  id: 534352,
  name: "Scroll",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Scrollscan",
      url: "https://scrollscan.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/moonbeam.js
var moonbeam = defineChain({
  id: 1284,
  name: "Moonbeam",
  nativeCurrency: {
    name: "Glimmer",
    symbol: "GLMR",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "moonscan",
      url: "https://moonbeam.moonscan.io"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/loot.js
var loot = defineChain({
  id: 5151706,
  name: "Loot Chain Mainnet",
  nativeCurrency: {
    name: "AGLD",
    symbol: "AGLD",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Explorer",
      url: "https://explorer.lootchain.com/"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/palm.js
var palm = defineChain({
  id: 11297108109,
  name: "Palm",
  nativeCurrency: {
    name: "PALM",
    symbol: "PALM",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Chainlens",
      url: "https://palm.chainlens.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/rari.js
var rari = defineChain({
  id: 1380012617,
  name: "Rarichain",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "rarichain-explorer",
      url: "https://mainnet.explorer.rarichain.org"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/god-woken.js
var godWoken = defineChain({
  id: 71402,
  name: "Godwoken Mainnet",
  nativeCurrency: {
    name: "pCKB",
    symbol: "pCKB",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "GWScan Block Explorer",
      url: "https://v1.gwscan.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/polygon-mumbai.js
var polygonMumbai = defineChain({
  id: 80001,
  name: "Polygon Mumbai",
  nativeCurrency: { name: "MATIC", symbol: "MATIC", decimals: 18 },
  blockExplorers: [
    {
      name: "PolygonScan",
      url: "https://mumbai.polygonscan.com",
      apiUrl: "https://mumbai.polygonscan.com/api"
    }
  ],
  testnet: true
});
var mumbai = polygonMumbai;

// node_modules/thirdweb/dist/esm/chains/chain-definitions/polygon-amoy.js
var polygonAmoy = defineChain({
  id: 80002,
  name: "Polygon Amoy",
  nativeCurrency: { name: "MATIC", symbol: "MATIC", decimals: 18 },
  blockExplorers: [
    {
      name: "PolygonScan",
      url: "https://amoy.polygonscan.com",
      apiUrl: "https://api-amoy.polygonscan.com/api"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/sepolia.js
var sepolia = defineChain({
  id: 11155111,
  name: "Sepolia",
  nativeCurrency: { name: "Sepolia Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Etherscan",
      url: "https://sepolia.etherscan.io",
      apiUrl: "https://api-sepolia.etherscan.io/api"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/bsc.js
var bsc = defineChain({
  id: 56,
  name: "BNB Smart Chain Mainnet",
  nativeCurrency: {
    name: "BNB Chain Native Token",
    symbol: "BNB",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "bscscan",
      url: "https://bscscan.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/bsc-testnet.js
var bscTestnet = defineChain({
  id: 97,
  name: "BNB Smart Chain Testnet",
  nativeCurrency: {
    name: "BNB Chain Native Token",
    symbol: "tBNB",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "bscscan-testnet",
      url: "https://testnet.bscscan.com"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/zksync.js
var zkSync = defineChain({
  id: 324,
  name: "ZkSync Era",
  nativeCurrency: {
    decimals: 18,
    name: "Ether",
    symbol: "ETH"
  },
  blockExplorers: [
    {
      name: "zkSync Era Block Explorer",
      url: "https://explorer.zksync.io",
      apiUrl: "https://block-explorer-api.zksync.dev/api"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/zksync-sepolia.js
var zkSyncSepolia = defineChain({
  id: 300,
  name: "ZkSync Sepolia",
  nativeCurrency: {
    decimals: 18,
    name: "Ether",
    symbol: "ETH"
  },
  blockExplorers: [
    {
      name: "zkSync Sepolia Block Explorer",
      url: "https://sepolia.explorer.zksync.io",
      apiUrl: "https://block-explorer-api.sepolia.zksync.dev/api"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/localhost.js
var localhost = defineChain({
  id: 1337,
  name: "Localhost",
  rpc: "http://127.0.0.1:8545",
  testnet: true,
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  }
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/zk-candy-sepolia.js
var zkCandySepolia = defineChain({
  id: 302,
  name: "zkCandy Sepolia Testnet",
  nativeCurrency: {
    decimals: 18,
    name: "Ether",
    symbol: "ETH"
  },
  blockExplorers: [
    {
      name: "zkCandy Block Explorer",
      url: "https://sepolia.explorer.zkcandy.io"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/fantom-testnet.js
var fantomTestnet = defineChain({
  id: 4002,
  name: "Fantom Testnet",
  nativeCurrency: {
    name: "Fantom",
    symbol: "FTM",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "ftmscan",
      url: "https://testnet.ftmscan.com"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/polygon-zkevm-testnet.js
var polygonZkEvmTestnet = defineChain({
  id: 1442,
  name: "Polygon zkEVM Testnet",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Polygon zkEVM explorer",
      url: "https://explorer.public.zkevm-test.net"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/gnosis-chiado-testnet.js
var gnosisChiadoTestnet = defineChain({
  id: 10200,
  name: "Gnosis Chiado Testnet",
  nativeCurrency: { name: "xDAI", symbol: "XDAI", decimals: 18 },
  blockExplorers: [
    {
      name: "blockscout",
      url: "https://gnosis-chiado.blockscout.com"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/blast-sepolia.js
var blastSepolia = defineChain({
  id: 168587773,
  name: "Blast Sepolia Testnet",
  nativeCurrency: { name: "Sepolia Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Blast Sepolia Explorer",
      url: "https://testnet.blastscan.io"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/manta-pacific-testnet.js
var mantaPacificTestnet = defineChain({
  id: 3441005,
  name: "Manta Pacific Testnet",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "manta-testnet Explorer",
      url: "https://manta-testnet.calderaexplorer.xyz"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/xai-sepolia.js
var xaiSepolia = defineChain({
  id: 37714555429,
  name: "Xai Sepolia",
  nativeCurrency: { name: "sXAI", symbol: "sXAI", decimals: 18 },
  blockExplorers: [
    {
      name: "Blockscout",
      url: "https://testnet-explorer-v2.xai-chain.net"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/scroll-alpha-testnet.js
var scrollAlphaTestnet = defineChain({
  id: 534353,
  name: "Scroll Alpha Testnet",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Scroll Alpha Testnet Block Explorer",
      url: "https://alpha-blockscout.scroll.io"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/scroll-sepolia-testnet.js
var scrollSepoliaTestnet = defineChain({
  id: 534353,
  name: "Scroll Sepolia Testnet",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Scroll Sepolia Etherscan",
      url: "https://sepolia.scrollscan.com"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/palm-testnet.js
var palmTestnet = defineChain({
  id: 11297108099,
  name: "Palm Testnet",
  nativeCurrency: {
    name: "PALM",
    symbol: "PALM",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Chainlens",
      url: "https://testnet.palm.chainlens.com"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/rari-testnet.js
var rariTestnet = defineChain({
  id: 1918988905,
  name: "RARIchain Testnet",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "rarichain-testnet-explorer",
      url: "https://explorer.rarichain.org"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/frame-testnet.js
var frameTestnet = defineChain({
  id: 68840142,
  name: "Frame Testnet",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Frame Testnet Explorer",
      url: "https://explorer.testnet.frame.xyz"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/hokum-testnet.js
var hokumTestnet = defineChain({
  id: 20482050,
  name: "Hokum Testnet",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Hokum Explorer",
      url: "https://testnet-explorer.hokum.gg"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/god-woken-testnet-v1.js
var godWokenTestnetV1 = defineChain({
  id: 71401,
  name: "Godwoken Testnet v1",
  nativeCurrency: {
    name: "pCKB",
    symbol: "pCKB",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "GWScan Block Explorer",
      url: "https://v1.testnet.gwscan.com"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/abstract-testnet.js
var abstractTestnet = defineChain({
  id: 11124,
  name: "Abstract Testnet",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Abstract Testnet Block Explorer",
      url: "https://explorer.testnet.abs.xyz"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/abstract.js
var abstract = defineChain({
  id: 2741,
  name: "Abstract",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Abstract Block Explorer",
      url: "https://explorer.abs.xyz"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/assetchain-testnet.js
var assetChainTestnet = defineChain({
  id: 42421,
  name: "AssetChain Testnet",
  nativeCurrency: {
    name: "Real World Asset",
    symbol: "RWA",
    decimals: 18
  },
  blockExplorers: [
    {
      name: "Asset Chain Testnet Explorer",
      url: "https://scan-testnet.assetchain.org",
      apiUrl: "https://scan-testnet.assetchain.org/api"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/celo-alfajores-testnet.js
var celoAlfajoresTestnet = defineChain({
  id: 44787,
  name: "Celo Alfajores Testnet",
  nativeCurrency: { name: "CELO", symbol: "CELO", decimals: 18 },
  blockExplorers: [
    {
      name: "Alfajoresscan",
      url: "https://alfajores.celoscan.io"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/fraxtal-testnet.js
var fraxtalTestnet = defineChain({
  id: 2522,
  name: "Fraxtal Testnet",
  nativeCurrency: { name: "Frax Ether", symbol: "frxETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Fraxscan",
      url: "https://holesky.fraxscan.com/"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/metal-l2-testnet.js
var metalL2Testnet = defineChain({
  id: 1740,
  name: "Metal L2 Testnet",
  nativeCurrency: { name: "ETH", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Blockscout",
      url: "https://testnet.explorer.metall2.com"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/mode-testnet.js
var modeTestnet = defineChain({
  id: 919,
  name: "Mode Testnet",
  nativeCurrency: { name: "Sepolia Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Modescout",
      url: "https://sepolia.explorer.mode.network/"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/mode.js
var mode = defineChain({
  id: 919,
  name: "Mode",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Modescout",
      url: "https://explorer.mode.network/"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/soneium-minato.js
var soneiumMinato = defineChain({
  id: 1946,
  name: "Soneium Minato",
  nativeCurrency: { name: "Ether", symbol: "ETH", decimals: 18 },
  blockExplorers: [
    {
      name: "Minato Explorer",
      url: "https://explorer-testnet.soneium.org/"
    }
  ],
  testnet: true
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/treasure.js
var treasure = defineChain({
  id: 61166,
  name: "Treasure",
  nativeCurrency: { name: "MAGIC", symbol: "MAGIC", decimals: 18 },
  blockExplorers: [
    {
      name: "Treasure Block Explorer",
      url: "https://treasurescan.io"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/treasureTopaz.js
var treasureTopaz = defineChain({
  id: 978658,
  name: "Treasure Topaz",
  nativeCurrency: { name: "MAGIC", symbol: "MAGIC", decimals: 18 },
  blockExplorers: [
    {
      name: "Treasure Topaz Block Explorer",
      url: "https://topaz.treasurescan.io"
    }
  ]
});

// node_modules/thirdweb/dist/esm/chains/chain-definitions/monad-testnet.js
var monadTestnet = defineChain({
  id: 10143,
  name: "Monad Testnet",
  nativeCurrency: { name: "Mon", symbol: "MON", decimals: 18 },
  blockExplorers: [
    {
      name: "Monad Explorer",
      url: "https://testnet.monadexplorer.com/"
    }
  ],
  testnet: true
});
export {
  abstract,
  abstractTestnet,
  anvil,
  arbitrum,
  arbitrumNova,
  arbitrumSepolia,
  assetChainTestnet,
  astriaEvmDusknet,
  avalanche,
  avalancheFuji,
  base,
  baseSepolia,
  blast,
  blastSepolia,
  bsc,
  bscTestnet,
  celo,
  celoAlfajoresTestnet,
  cronos,
  defineChain,
  degen,
  ethereum,
  fantom,
  fantomTestnet,
  frameTestnet,
  fraxtalTestnet,
  getChainMetadata,
  getRpcUrlForChain,
  gnosis,
  gnosisChiadoTestnet,
  godWoken,
  godWokenTestnetV1,
  hardhat,
  hokumTestnet,
  linea,
  lineaSepolia,
  localhost,
  loot,
  mainnet,
  mantaPacific,
  mantaPacificTestnet,
  metalL2Testnet,
  mode,
  modeTestnet,
  monadTestnet,
  moonbeam,
  mumbai,
  optimism,
  optimismSepolia,
  palm,
  palmTestnet,
  polygon,
  polygonAmoy,
  polygonMumbai,
  polygonZkEvm,
  polygonZkEvmTestnet,
  rari,
  rariTestnet,
  scroll,
  scrollAlphaTestnet,
  scrollSepoliaTestnet,
  sepolia,
  soneiumMinato,
  treasure,
  treasureTopaz,
  xai,
  xaiSepolia,
  zkCandySepolia,
  zkSync,
  zkSyncSepolia,
  zora,
  zoraSepolia
};
//# sourceMappingURL=thirdweb_chains.js.map
