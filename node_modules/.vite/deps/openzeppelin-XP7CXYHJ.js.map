{"version": 3, "sources": ["../../thirdweb/src/transaction/actions/gasless/providers/openzeppelin.ts"], "sourcesContent": ["import type { Address } from \"abitype\";\nimport { getContract } from \"../../../../contract/contract.js\";\nimport { isHex } from \"../../../../utils/encoding/helpers/is-hex.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { Account } from \"../../../../wallets/interfaces/wallet.js\";\nimport type { PreparedTransaction } from \"../../../prepare-transaction.js\";\nimport { readContract } from \"../../../read-contract.js\";\nimport type { SerializableTransaction } from \"../../../serialize-transaction.js\";\nimport type { WaitForReceiptOptions } from \"../../wait-for-tx-receipt.js\";\n\n/**\n * @transaction\n */\nexport type OpenZeppelinOptions = {\n  provider: \"openzeppelin\";\n  relayerUrl: string;\n  relayerForwarderAddress: Address;\n  domainName?: string; // default: \"GSNv2 Forwarder\"\n  domainVersion?: string; // default: \"0.0.1\"\n  domainSeparatorVersion?: string; // default: \"1\"\n  experimentalChainlessSupport?: boolean; // default: false\n};\n\ntype SendOpenZeppelinTransactionOptions = {\n  account: Account;\n  // TODO: update this to `Transaction<\"prepared\">` once the type is available to ensure only prepared transactions are accepted\n  // biome-ignore lint/suspicious/noExplicitAny: library function that accepts any prepared transaction type\n  transaction: PreparedTransaction<any>;\n  serializableTransaction: SerializableTransaction;\n  gasless: OpenZeppelinOptions;\n};\n\n/**\n * @internal - only exported for testing\n */\nexport async function prepareOpenZeppelinTransaction({\n  account,\n  serializableTransaction,\n  transaction,\n  gasless,\n}: SendOpenZeppelinTransactionOptions) {\n  const forrwaderContract = getContract({\n    address: gasless.relayerForwarderAddress,\n    chain: transaction.chain,\n    client: transaction.client,\n  });\n\n  const nonce = await readContract({\n    contract: forrwaderContract,\n    method: \"function getNonce(address) view returns (uint256)\",\n    params: [account.address],\n  });\n\n  const [signature, message] = await (async () => {\n    // TODO: handle special case for `approve` -> `permit` transactions\n\n    if (!serializableTransaction.to) {\n      throw new Error(\"OpenZeppelin transactions must have a 'to' address\");\n    }\n    if (!serializableTransaction.gas) {\n      throw new Error(\"OpenZeppelin transactions must have a 'gas' value\");\n    }\n    if (!serializableTransaction.data) {\n      throw new Error(\"OpenZeppelin transactions must have a 'data' value\");\n    }\n    // chainless support!\n    if (gasless.experimentalChainlessSupport) {\n      const message = {\n        from: account.address,\n        to: serializableTransaction.to,\n        value: 0n,\n        gas: serializableTransaction.gas,\n        nonce: nonce,\n        data: serializableTransaction.data,\n        chainid: BigInt(transaction.chain.id),\n      } as const;\n      return [\n        await account.signTypedData({\n          domain: {\n            name: \"GSNv2 Forwarder\",\n            version: \"0.0.1\",\n            verifyingContract: forrwaderContract.address,\n          },\n          message,\n          primaryType: \"ForwardRequest\",\n          types: { ForwardRequest: ChainAwareForwardRequest },\n        }),\n        message,\n      ] as const;\n    }\n    // else non-chainless support\n    const message = {\n      from: account.address,\n      to: serializableTransaction.to,\n      value: 0n,\n      gas: serializableTransaction.gas,\n      nonce: nonce,\n      data: serializableTransaction.data,\n    } as const;\n    return [\n      await account.signTypedData({\n        domain: {\n          name: gasless.domainName ?? \"GSNv2 Forwarder\",\n          version: gasless.domainVersion ?? \"0.0.1\",\n          chainId: transaction.chain.id,\n          verifyingContract: forrwaderContract.address,\n        },\n        message,\n        primaryType: \"ForwardRequest\",\n        types: { ForwardRequest },\n      }),\n      message,\n    ] as const;\n  })();\n  // TODO: handle special case for `approve` -> `permit`\n  const messageType = \"forward\";\n\n  return { message, signature, messageType } as const;\n}\n\nconst ForwardRequest = [\n  { name: \"from\", type: \"address\" },\n  { name: \"to\", type: \"address\" },\n  { name: \"value\", type: \"uint256\" },\n  { name: \"gas\", type: \"uint256\" },\n  { name: \"nonce\", type: \"uint256\" },\n  { name: \"data\", type: \"bytes\" },\n] as const;\n\nconst ChainAwareForwardRequest = [\n  { name: \"from\", type: \"address\" },\n  { name: \"to\", type: \"address\" },\n  { name: \"value\", type: \"uint256\" },\n  { name: \"gas\", type: \"uint256\" },\n  { name: \"nonce\", type: \"uint256\" },\n  { name: \"data\", type: \"bytes\" },\n  { name: \"chainid\", type: \"uint256\" },\n] as const;\n\n/**\n * @internal\n */\nexport async function relayOpenZeppelinTransaction(\n  options: SendOpenZeppelinTransactionOptions,\n): Promise<WaitForReceiptOptions> {\n  const { message, messageType, signature } =\n    await prepareOpenZeppelinTransaction(options);\n\n  const response = await fetch(options.gasless.relayerUrl, {\n    method: \"POST\",\n    body: stringify({\n      request: message,\n      type: messageType,\n      signature,\n      forwarderAddress: options.gasless.relayerForwarderAddress,\n    }),\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to send transaction: ${await response.text()}`);\n  }\n  const json = await response.json();\n  if (!json.result) {\n    throw new Error(`Relay transaction failed: ${json.message}`);\n  }\n  const transactionHash = JSON.parse(json.result).txHash;\n  if (isHex(transactionHash)) {\n    return {\n      transactionHash,\n      chain: options.transaction.chain,\n      client: options.transaction.client,\n    };\n  }\n\n  throw new Error(`Failed to send transaction: ${stringify(json)}`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,eAAsB,+BAA+B,EACnD,SACA,yBACA,aACA,QAAO,GAC4B;AACnC,QAAM,oBAAoB,YAAY;IACpC,SAAS,QAAQ;IACjB,OAAO,YAAY;IACnB,QAAQ,YAAY;GACrB;AAED,QAAM,QAAQ,MAAM,aAAa;IAC/B,UAAU;IACV,QAAQ;IACR,QAAQ,CAAC,QAAQ,OAAO;GACzB;AAED,QAAM,CAAC,WAAW,OAAO,IAAI,OAAO,YAAW;AAG7C,QAAI,CAAC,wBAAwB,IAAI;AAC/B,YAAM,IAAI,MAAM,oDAAoD;IACtE;AACA,QAAI,CAAC,wBAAwB,KAAK;AAChC,YAAM,IAAI,MAAM,mDAAmD;IACrE;AACA,QAAI,CAAC,wBAAwB,MAAM;AACjC,YAAM,IAAI,MAAM,oDAAoD;IACtE;AAEA,QAAI,QAAQ,8BAA8B;AACxC,YAAMA,WAAU;QACd,MAAM,QAAQ;QACd,IAAI,wBAAwB;QAC5B,OAAO;QACP,KAAK,wBAAwB;QAC7B;QACA,MAAM,wBAAwB;QAC9B,SAAS,OAAO,YAAY,MAAM,EAAE;;AAEtC,aAAO;QACL,MAAM,QAAQ,cAAc;UAC1B,QAAQ;YACN,MAAM;YACN,SAAS;YACT,mBAAmB,kBAAkB;;UAEvC,SAAAA;UACA,aAAa;UACb,OAAO,EAAE,gBAAgB,yBAAwB;SAClD;QACDA;;IAEJ;AAEA,UAAMA,WAAU;MACd,MAAM,QAAQ;MACd,IAAI,wBAAwB;MAC5B,OAAO;MACP,KAAK,wBAAwB;MAC7B;MACA,MAAM,wBAAwB;;AAEhC,WAAO;MACL,MAAM,QAAQ,cAAc;QAC1B,QAAQ;UACN,MAAM,QAAQ,cAAc;UAC5B,SAAS,QAAQ,iBAAiB;UAClC,SAAS,YAAY,MAAM;UAC3B,mBAAmB,kBAAkB;;QAEvC,SAAAA;QACA,aAAa;QACb,OAAO,EAAE,eAAc;OACxB;MACDA;;EAEJ,GAAE;AAEF,QAAM,cAAc;AAEpB,SAAO,EAAE,SAAS,WAAW,YAAW;AAC1C;AAEA,IAAM,iBAAiB;EACrB,EAAE,MAAM,QAAQ,MAAM,UAAS;EAC/B,EAAE,MAAM,MAAM,MAAM,UAAS;EAC7B,EAAE,MAAM,SAAS,MAAM,UAAS;EAChC,EAAE,MAAM,OAAO,MAAM,UAAS;EAC9B,EAAE,MAAM,SAAS,MAAM,UAAS;EAChC,EAAE,MAAM,QAAQ,MAAM,QAAO;;AAG/B,IAAM,2BAA2B;EAC/B,EAAE,MAAM,QAAQ,MAAM,UAAS;EAC/B,EAAE,MAAM,MAAM,MAAM,UAAS;EAC7B,EAAE,MAAM,SAAS,MAAM,UAAS;EAChC,EAAE,MAAM,OAAO,MAAM,UAAS;EAC9B,EAAE,MAAM,SAAS,MAAM,UAAS;EAChC,EAAE,MAAM,QAAQ,MAAM,QAAO;EAC7B,EAAE,MAAM,WAAW,MAAM,UAAS;;AAMpC,eAAsB,6BACpB,SAA2C;AAE3C,QAAM,EAAE,SAAS,aAAa,UAAS,IACrC,MAAM,+BAA+B,OAAO;AAE9C,QAAM,WAAW,MAAM,MAAM,QAAQ,QAAQ,YAAY;IACvD,QAAQ;IACR,MAAM,UAAU;MACd,SAAS;MACT,MAAM;MACN;MACA,kBAAkB,QAAQ,QAAQ;KACnC;GACF;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,+BAA+B,MAAM,SAAS,KAAI,CAAE,EAAE;EACxE;AACA,QAAM,OAAO,MAAM,SAAS,KAAI;AAChC,MAAI,CAAC,KAAK,QAAQ;AAChB,UAAM,IAAI,MAAM,6BAA6B,KAAK,OAAO,EAAE;EAC7D;AACA,QAAM,kBAAkB,KAAK,MAAM,KAAK,MAAM,EAAE;AAChD,MAAI,MAAM,eAAe,GAAG;AAC1B,WAAO;MACL;MACA,OAAO,QAAQ,YAAY;MAC3B,QAAQ,QAAQ,YAAY;;EAEhC;AAEA,QAAM,IAAI,MAAM,+BAA+B,UAAU,IAAI,CAAC,EAAE;AAClE;", "names": ["message"]}