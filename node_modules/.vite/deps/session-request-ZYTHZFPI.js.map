{"version": 3, "sources": ["../../thirdweb/src/wallets/wallet-connect/receiver/session-request.ts"], "sourcesContent": ["import type { ThirdwebClient } from \"../../../client/client.js\";\nimport type { Hex } from \"../../../utils/encoding/hex.js\";\nimport type { Wallet } from \"../../interfaces/wallet.js\";\nimport { handleSendRawTransactionRequest } from \"./request-handlers/send-raw-transaction.js\";\nimport { handleSendTransactionRequest } from \"./request-handlers/send-transaction.js\";\nimport { handleSignTransactionRequest } from \"./request-handlers/sign-transaction.js\";\nimport { handleSignTypedDataRequest } from \"./request-handlers/sign-typed-data.js\";\n// Due to some edge cases, we can't import these handlers dynamically\nimport { handleSignRequest } from \"./request-handlers/sign.js\";\nimport type {\n  WalletConnectAddEthereumChainRequestParams,\n  WalletConnectClient,\n  WalletConnectSwitchEthereumChainRequestParams,\n} from \"./types.js\";\nimport type {\n  WalletConnectRawTransactionRequestParams,\n  WalletConnectRequestError,\n  WalletConnectRequestHandlers,\n  WalletConnectSessionRequestEvent,\n  WalletConnectSignRequestPrams,\n  WalletConnectSignTypedDataRequestParams,\n  WalletConnectTransactionRequestParams,\n} from \"./types.js\";\nimport { parseEip155ChainId } from \"./utils.js\";\n\n/**\n * @internal\n */\nexport async function fulfillRequest(options: {\n  wallet: Wallet;\n  walletConnectClient: WalletConnectClient;\n  event: WalletConnectSessionRequestEvent;\n  thirdwebClient: ThirdwebClient;\n  handlers?: WalletConnectRequestHandlers;\n}) {\n  const {\n    wallet,\n    walletConnectClient,\n    thirdwebClient,\n    event: {\n      topic,\n      id,\n      params: { chainId: rawChainId, request },\n    },\n    handlers,\n  } = options;\n\n  const account = wallet.getAccount();\n  if (!account) {\n    throw new Error(\"No account connected to provided wallet\");\n  }\n\n  let result: WalletConnectRequestError | Hex;\n  try {\n    switch (request.method) {\n      case \"personal_sign\": {\n        if (handlers?.personal_sign) {\n          result = await handlers.personal_sign({\n            account,\n            params: request.params as WalletConnectSignRequestPrams,\n          });\n        } else {\n          result = await handleSignRequest({\n            account,\n            params: request.params as WalletConnectSignRequestPrams,\n          });\n        }\n        break;\n      }\n      case \"eth_sign\": {\n        if (handlers?.eth_sign) {\n          result = await handlers.eth_sign({\n            account,\n            params: request.params as WalletConnectSignRequestPrams,\n          });\n        } else {\n          result = await handleSignRequest({\n            account,\n            params: request.params as WalletConnectSignRequestPrams,\n          });\n        }\n        break;\n      }\n      case \"eth_signTypedData\": {\n        if (handlers?.eth_signTypedData) {\n          result = await handlers.eth_signTypedData({\n            account,\n            params: request.params as WalletConnectSignTypedDataRequestParams,\n          });\n        } else {\n          result = await handleSignTypedDataRequest({\n            account,\n            params: request.params as WalletConnectSignTypedDataRequestParams,\n          });\n        }\n        break;\n      }\n      case \"eth_signTypedData_v4\": {\n        if (handlers?.eth_signTypedData_v4) {\n          result = await handlers.eth_signTypedData_v4({\n            account,\n            params: request.params as WalletConnectSignTypedDataRequestParams,\n          });\n        } else {\n          result = await handleSignTypedDataRequest({\n            account,\n            params: request.params as WalletConnectSignTypedDataRequestParams,\n          });\n        }\n        break;\n      }\n      case \"eth_signTransaction\": {\n        if (handlers?.eth_signTransaction) {\n          result = await handlers.eth_signTransaction({\n            account,\n            params: request.params as WalletConnectTransactionRequestParams,\n          });\n        } else {\n          result = await handleSignTransactionRequest({\n            account,\n            params: request.params as WalletConnectTransactionRequestParams,\n          });\n        }\n        break;\n      }\n      case \"eth_sendTransaction\": {\n        const chainId = parseEip155ChainId(rawChainId);\n        if (handlers?.eth_sendTransaction) {\n          result = await handlers.eth_sendTransaction({\n            account,\n            chainId,\n            params: request.params as WalletConnectTransactionRequestParams,\n          });\n        } else {\n          result = await handleSendTransactionRequest({\n            account,\n            chainId,\n            thirdwebClient,\n            params: request.params as WalletConnectTransactionRequestParams,\n          });\n        }\n        break;\n      }\n      case \"eth_sendRawTransaction\": {\n        const chainId = parseEip155ChainId(rawChainId);\n        if (handlers?.eth_sendRawTransaction) {\n          result = await handlers.eth_sendRawTransaction({\n            account,\n            chainId,\n            params: request.params as WalletConnectRawTransactionRequestParams,\n          });\n        } else {\n          result = await handleSendRawTransactionRequest({\n            account,\n            chainId,\n            params: request.params as WalletConnectRawTransactionRequestParams,\n          });\n        }\n        break;\n      }\n      case \"wallet_addEthereumChain\": {\n        if (handlers?.wallet_addEthereumChain) {\n          result = await handlers.wallet_addEthereumChain({\n            wallet,\n            params:\n              request.params as WalletConnectAddEthereumChainRequestParams,\n          });\n        } else {\n          throw new Error(\n            \"Unsupported request method: wallet_addEthereumChain\",\n          );\n        }\n        break;\n      }\n      case \"wallet_switchEthereumChain\": {\n        if (handlers?.wallet_switchEthereumChain) {\n          result = await handlers.wallet_switchEthereumChain({\n            wallet,\n            params:\n              request.params as WalletConnectSwitchEthereumChainRequestParams,\n          });\n        } else {\n          const { handleSwitchChain } = await import(\n            \"./request-handlers/switch-chain.js\"\n          );\n\n          result = await handleSwitchChain({\n            wallet,\n            params:\n              request.params as WalletConnectSwitchEthereumChainRequestParams,\n          });\n        }\n        break;\n      }\n      default: {\n        const potentialHandler = handlers?.[request.method];\n        if (potentialHandler) {\n          result = await potentialHandler({\n            account,\n            chainId: parseEip155ChainId(rawChainId),\n            params: request.params,\n          });\n        } else {\n          throw new Error(`Unsupported request method: ${request.method}`);\n        }\n      }\n    }\n  } catch (error: unknown) {\n    result = {\n      code:\n        typeof error === \"object\" && error !== null && \"code\" in error\n          ? (error as { code: number }).code\n          : 500,\n      message:\n        typeof error === \"object\" && error !== null && \"message\" in error\n          ? (error as { message: string }).message\n          : \"Unknown error\",\n    };\n  }\n\n  walletConnectClient.respond({\n    topic,\n    response: {\n      id,\n      jsonrpc: \"2.0\",\n      result,\n    },\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,eAAsB,eAAe,SAMpC;AACC,QAAM,EACJ,QACA,qBACA,gBACA,OAAO,EACL,OACA,IACA,QAAQ,EAAE,SAAS,YAAY,QAAO,EAAE,GAE1C,SAAQ,IACN;AAEJ,QAAM,UAAU,OAAO,WAAU;AACjC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,yCAAyC;EAC3D;AAEA,MAAI;AACJ,MAAI;AACF,YAAQ,QAAQ,QAAQ;MACtB,KAAK,iBAAiB;AACpB,YAAI,qCAAU,eAAe;AAC3B,mBAAS,MAAM,SAAS,cAAc;YACpC;YACA,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,mBAAS,MAAM,kBAAkB;YAC/B;YACA,QAAQ,QAAQ;WACjB;QACH;AACA;MACF;MACA,KAAK,YAAY;AACf,YAAI,qCAAU,UAAU;AACtB,mBAAS,MAAM,SAAS,SAAS;YAC/B;YACA,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,mBAAS,MAAM,kBAAkB;YAC/B;YACA,QAAQ,QAAQ;WACjB;QACH;AACA;MACF;MACA,KAAK,qBAAqB;AACxB,YAAI,qCAAU,mBAAmB;AAC/B,mBAAS,MAAM,SAAS,kBAAkB;YACxC;YACA,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,mBAAS,MAAM,2BAA2B;YACxC;YACA,QAAQ,QAAQ;WACjB;QACH;AACA;MACF;MACA,KAAK,wBAAwB;AAC3B,YAAI,qCAAU,sBAAsB;AAClC,mBAAS,MAAM,SAAS,qBAAqB;YAC3C;YACA,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,mBAAS,MAAM,2BAA2B;YACxC;YACA,QAAQ,QAAQ;WACjB;QACH;AACA;MACF;MACA,KAAK,uBAAuB;AAC1B,YAAI,qCAAU,qBAAqB;AACjC,mBAAS,MAAM,SAAS,oBAAoB;YAC1C;YACA,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,mBAAS,MAAM,6BAA6B;YAC1C;YACA,QAAQ,QAAQ;WACjB;QACH;AACA;MACF;MACA,KAAK,uBAAuB;AAC1B,cAAM,UAAU,mBAAmB,UAAU;AAC7C,YAAI,qCAAU,qBAAqB;AACjC,mBAAS,MAAM,SAAS,oBAAoB;YAC1C;YACA;YACA,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,mBAAS,MAAM,6BAA6B;YAC1C;YACA;YACA;YACA,QAAQ,QAAQ;WACjB;QACH;AACA;MACF;MACA,KAAK,0BAA0B;AAC7B,cAAM,UAAU,mBAAmB,UAAU;AAC7C,YAAI,qCAAU,wBAAwB;AACpC,mBAAS,MAAM,SAAS,uBAAuB;YAC7C;YACA;YACA,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,mBAAS,MAAM,gCAAgC;YAC7C;YACA;YACA,QAAQ,QAAQ;WACjB;QACH;AACA;MACF;MACA,KAAK,2BAA2B;AAC9B,YAAI,qCAAU,yBAAyB;AACrC,mBAAS,MAAM,SAAS,wBAAwB;YAC9C;YACA,QACE,QAAQ;WACX;QACH,OAAO;AACL,gBAAM,IAAI,MACR,qDAAqD;QAEzD;AACA;MACF;MACA,KAAK,8BAA8B;AACjC,YAAI,qCAAU,4BAA4B;AACxC,mBAAS,MAAM,SAAS,2BAA2B;YACjD;YACA,QACE,QAAQ;WACX;QACH,OAAO;AACL,gBAAM,EAAE,kBAAiB,IAAK,MAAM,OAClC,4BAAoC;AAGtC,mBAAS,MAAM,kBAAkB;YAC/B;YACA,QACE,QAAQ;WACX;QACH;AACA;MACF;MACA,SAAS;AACP,cAAM,mBAAmB,qCAAW,QAAQ;AAC5C,YAAI,kBAAkB;AACpB,mBAAS,MAAM,iBAAiB;YAC9B;YACA,SAAS,mBAAmB,UAAU;YACtC,QAAQ,QAAQ;WACjB;QACH,OAAO;AACL,gBAAM,IAAI,MAAM,+BAA+B,QAAQ,MAAM,EAAE;QACjE;MACF;IACF;EACF,SAAS,OAAgB;AACvB,aAAS;MACP,MACE,OAAO,UAAU,YAAY,UAAU,QAAQ,UAAU,QACpD,MAA2B,OAC5B;MACN,SACE,OAAO,UAAU,YAAY,UAAU,QAAQ,aAAa,QACvD,MAA8B,UAC/B;;EAEV;AAEA,sBAAoB,QAAQ;IAC1B;IACA,UAAU;MACR;MACA,SAAS;MACT;;GAEH;AACH;", "names": []}