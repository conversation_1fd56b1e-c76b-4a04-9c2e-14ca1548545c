{"version": 3, "sources": ["../../thirdweb/src/extensions/modules/__generated__/IModularCore/read/getInstalledModules.ts"], "sourcesContent": ["import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0x3e429396\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"tuple[]\",\n    components: [\n      {\n        type: \"address\",\n        name: \"implementation\",\n      },\n      {\n        type: \"tuple\",\n        name: \"config\",\n        components: [\n          {\n            type: \"bool\",\n            name: \"registerInstallationCallback\",\n          },\n          {\n            type: \"bytes4[]\",\n            name: \"requiredInterfaces\",\n          },\n          {\n            type: \"bytes4[]\",\n            name: \"supportedInterfaces\",\n          },\n          {\n            type: \"tuple[]\",\n            name: \"callbackFunctions\",\n            components: [\n              {\n                type: \"bytes4\",\n                name: \"selector\",\n              },\n            ],\n          },\n          {\n            type: \"tuple[]\",\n            name: \"fallbackFunctions\",\n            components: [\n              {\n                type: \"bytes4\",\n                name: \"selector\",\n              },\n              {\n                type: \"uint256\",\n                name: \"permissionBits\",\n              },\n            ],\n          },\n        ],\n      },\n    ],\n  },\n] as const;\n\n/**\n * Checks if the `getInstalledModules` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getInstalledModules` method is supported.\n * @extension MODULES\n * @example\n * ```ts\n * import { isGetInstalledModulesSupported } from \"thirdweb/extensions/modules\";\n * const supported = isGetInstalledModulesSupported([\"0x...\"]);\n * ```\n */\nexport function isGetInstalledModulesSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the getInstalledModules function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension MODULES\n * @example\n * ```ts\n * import { decodeGetInstalledModulesResult } from \"thirdweb/extensions/modules\";\n * const result = decodeGetInstalledModulesResultResult(\"...\");\n * ```\n */\nexport function decodeGetInstalledModulesResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getInstalledModules\" function on the contract.\n * @param options - The options for the getInstalledModules function.\n * @returns The parsed result of the function call.\n * @extension MODULES\n * @example\n * ```ts\n * import { getInstalledModules } from \"thirdweb/extensions/modules\";\n *\n * const result = await getInstalledModules({\n *  contract,\n * });\n *\n * ```\n */\nexport async function getInstalledModules(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,cAAc;AAC3B,IAAM,YAAY,CAAA;AAClB,IAAM,aAAa;EACjB;IACE,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;QACN,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;YACN,YAAY;cACV;gBACE,MAAM;gBACN,MAAM;;;;UAIZ;YACE,MAAM;YACN,MAAM;YACN,YAAY;cACV;gBACE,MAAM;gBACN,MAAM;;cAER;gBACE,MAAM;gBACN,MAAM;;;;;;;;;AAqBhB,SAAU,+BAA+B,oBAA4B;AACzE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAaM,SAAU,gCAAgC,QAAW;AACzD,SAAO,oBAAoB,YAAY,MAAM,EAAE,CAAC;AAClD;AAiBA,eAAsB,oBAAoB,SAA+B;AACvE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAA;GACT;AACH;", "names": []}