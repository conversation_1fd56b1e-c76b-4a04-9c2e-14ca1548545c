{"version": 3, "sources": ["../../thirdweb/src/wallets/wallet-connect/receiver/session-proposal.ts"], "sourcesContent": ["import type { Chain } from \"../../../chains/types.js\";\nimport type { Account, Wallet } from \"../../interfaces/wallet.js\";\nimport { disconnectWalletConnectSession } from \"./index.js\";\nimport { getSessions, saveSession } from \"./session-store.js\";\nimport type { WalletConnectClient } from \"./types.js\";\nimport type {\n  WalletConnectSession,\n  WalletConnectSessionProposalEvent,\n} from \"./types.js\";\n\n/**\n * @internal\n */\nexport async function onSessionProposal(options: {\n  wallet: Wallet;\n  walletConnectClient: WalletConnectClient;\n  event: WalletConnectSessionProposalEvent;\n  chains?: Chain[];\n  onConnect?: (session: WalletConnectSession) => void;\n}) {\n  const { wallet, walletConnectClient, event, chains, onConnect } = options;\n\n  const account = wallet.getAccount();\n  if (!account) {\n    throw new Error(\"No account connected to provided wallet\");\n  }\n\n  const origin = event.verifyContext?.verified?.origin;\n  if (origin) {\n    await disconnectExistingSessions({ origin, walletConnectClient });\n  }\n  const session = await acceptSessionProposal({\n    account,\n    walletConnectClient,\n    sessionProposal: event,\n    chains,\n  });\n\n  await saveSession(session);\n\n  wallet.subscribe(\"disconnect\", () => {\n    disconnectWalletConnectSession({ session, walletConnectClient });\n  });\n\n  onConnect?.(session);\n}\n\n/**\n * @internal\n */\nexport async function disconnectExistingSessions({\n  walletConnectClient,\n  origin,\n}: { walletConnectClient: WalletConnectClient; origin: string }) {\n  const sessions = await getSessions();\n  for (const session of sessions) {\n    if (session.origin === origin) {\n      await disconnectWalletConnectSession({ session, walletConnectClient });\n    }\n  }\n}\n\n/**\n * @internal\n */\nexport async function acceptSessionProposal({\n  account,\n  walletConnectClient,\n  sessionProposal,\n  chains,\n}: {\n  account: Account;\n  walletConnectClient: WalletConnectClient;\n  sessionProposal: WalletConnectSessionProposalEvent;\n  chains?: Chain[];\n}): Promise<WalletConnectSession> {\n  if (\n    !sessionProposal.params.requiredNamespaces?.eip155 &&\n    !sessionProposal.params.optionalNamespaces?.eip155\n  ) {\n    throw new Error(\n      \"No EIP155 namespace found in Wallet Connect session proposal\",\n    );\n  }\n\n  const namespaces = {\n    chains: [\n      ...Array.from(\n        new Set([\n          ...(sessionProposal.params.requiredNamespaces?.eip155?.chains?.map(\n            (chain: string) => `${chain}:${account.address}`,\n          ) ?? []),\n          ...(sessionProposal.params.optionalNamespaces?.eip155?.chains?.map(\n            (chain: string) => `${chain}:${account.address}`,\n          ) ?? []),\n          ...(chains?.map((chain) => `eip155:${chain.id}:${account.address}`) ??\n            []),\n        ]),\n      ),\n    ],\n    methods: [\n      ...(sessionProposal.params.requiredNamespaces?.eip155?.methods ?? []),\n      ...(sessionProposal.params.optionalNamespaces?.eip155?.methods ?? []),\n    ],\n    events: [\n      ...(sessionProposal.params.requiredNamespaces?.eip155?.events ?? []),\n      ...(sessionProposal.params.optionalNamespaces?.eip155?.events ?? []),\n    ],\n  };\n  const approval = await walletConnectClient.approve({\n    id: sessionProposal.id,\n    namespaces: {\n      eip155: {\n        accounts: namespaces.chains,\n        methods: namespaces.methods,\n        events: namespaces.events,\n      },\n    },\n  });\n\n  const session = await approval.acknowledged();\n  return {\n    topic: session.topic,\n    origin: sessionProposal.verifyContext?.verified?.origin || \"Unknown origin\",\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,eAAsB,kBAAkB,SAMvC;AAjBD;AAkBE,QAAM,EAAE,QAAQ,qBAAqB,OAAO,QAAQ,UAAS,IAAK;AAElE,QAAM,UAAU,OAAO,WAAU;AACjC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,yCAAyC;EAC3D;AAEA,QAAM,UAAS,iBAAM,kBAAN,mBAAqB,aAArB,mBAA+B;AAC9C,MAAI,QAAQ;AACV,UAAM,2BAA2B,EAAE,QAAQ,oBAAmB,CAAE;EAClE;AACA,QAAM,UAAU,MAAM,sBAAsB;IAC1C;IACA;IACA,iBAAiB;IACjB;GACD;AAED,QAAM,YAAY,OAAO;AAEzB,SAAO,UAAU,cAAc,MAAK;AAClC,mCAA+B,EAAE,SAAS,oBAAmB,CAAE;EACjE,CAAC;AAED,yCAAY;AACd;AAKA,eAAsB,2BAA2B,EAC/C,qBACA,OAAM,GACuD;AAC7D,QAAM,WAAW,MAAM,YAAW;AAClC,aAAW,WAAW,UAAU;AAC9B,QAAI,QAAQ,WAAW,QAAQ;AAC7B,YAAM,+BAA+B,EAAE,SAAS,oBAAmB,CAAE;IACvE;EACF;AACF;AAKA,eAAsB,sBAAsB,EAC1C,SACA,qBACA,iBACA,OAAM,GAMP;AAzED;AA0EE,MACE,GAAC,qBAAgB,OAAO,uBAAvB,mBAA2C,WAC5C,GAAC,qBAAgB,OAAO,uBAAvB,mBAA2C,SAC5C;AACA,UAAM,IAAI,MACR,8DAA8D;EAElE;AAEA,QAAM,aAAa;IACjB,QAAQ;MACN,GAAG,MAAM,KACP,oBAAI,IAAI;QACN,KAAI,iCAAgB,OAAO,uBAAvB,mBAA2C,WAA3C,mBAAmD,WAAnD,mBAA2D,IAC7D,CAAC,UAAkB,GAAG,KAAK,IAAI,QAAQ,OAAO,QAC3C,CAAA;QACL,KAAI,iCAAgB,OAAO,uBAAvB,mBAA2C,WAA3C,mBAAmD,WAAnD,mBAA2D,IAC7D,CAAC,UAAkB,GAAG,KAAK,IAAI,QAAQ,OAAO,QAC3C,CAAA;QACL,IAAI,iCAAQ,IAAI,CAAC,UAAU,UAAU,MAAM,EAAE,IAAI,QAAQ,OAAO,QAC9D,CAAA;OACH,CAAC;;IAGN,SAAS;MACP,KAAI,2BAAgB,OAAO,uBAAvB,mBAA2C,WAA3C,mBAAmD,YAAW,CAAA;MAClE,KAAI,2BAAgB,OAAO,uBAAvB,mBAA2C,WAA3C,mBAAmD,YAAW,CAAA;;IAEpE,QAAQ;MACN,KAAI,2BAAgB,OAAO,uBAAvB,mBAA2C,WAA3C,mBAAmD,WAAU,CAAA;MACjE,KAAI,2BAAgB,OAAO,uBAAvB,mBAA2C,WAA3C,mBAAmD,WAAU,CAAA;;;AAGrE,QAAM,WAAW,MAAM,oBAAoB,QAAQ;IACjD,IAAI,gBAAgB;IACpB,YAAY;MACV,QAAQ;QACN,UAAU,WAAW;QACrB,SAAS,WAAW;QACpB,QAAQ,WAAW;;;GAGxB;AAED,QAAM,UAAU,MAAM,SAAS,aAAY;AAC3C,SAAO;IACL,OAAO,QAAQ;IACf,UAAQ,2BAAgB,kBAAhB,mBAA+B,aAA/B,mBAAyC,WAAU;;AAE/D;", "names": []}