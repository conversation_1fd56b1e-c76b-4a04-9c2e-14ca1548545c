import {
  readContract
} from "./chunk-A2MKHEYJ.js";
import "./chunk-TVIYKLDL.js";
import "./chunk-JOYHE2MS.js";
import "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import {
  stringify
} from "./chunk-2CIJO3V3.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import {
  isHex
} from "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/transaction/actions/gasless/providers/openzeppelin.js
async function prepareOpenZeppelinTransaction({ account, serializableTransaction, transaction, gasless }) {
  const forrwaderContract = getContract({
    address: gasless.relayerForwarderAddress,
    chain: transaction.chain,
    client: transaction.client
  });
  const nonce = await readContract({
    contract: forrwaderContract,
    method: "function getNonce(address) view returns (uint256)",
    params: [account.address]
  });
  const [signature, message] = await (async () => {
    if (!serializableTransaction.to) {
      throw new Error("OpenZeppelin transactions must have a 'to' address");
    }
    if (!serializableTransaction.gas) {
      throw new Error("OpenZeppelin transactions must have a 'gas' value");
    }
    if (!serializableTransaction.data) {
      throw new Error("OpenZeppelin transactions must have a 'data' value");
    }
    if (gasless.experimentalChainlessSupport) {
      const message3 = {
        from: account.address,
        to: serializableTransaction.to,
        value: 0n,
        gas: serializableTransaction.gas,
        nonce,
        data: serializableTransaction.data,
        chainid: BigInt(transaction.chain.id)
      };
      return [
        await account.signTypedData({
          domain: {
            name: "GSNv2 Forwarder",
            version: "0.0.1",
            verifyingContract: forrwaderContract.address
          },
          message: message3,
          primaryType: "ForwardRequest",
          types: { ForwardRequest: ChainAwareForwardRequest }
        }),
        message3
      ];
    }
    const message2 = {
      from: account.address,
      to: serializableTransaction.to,
      value: 0n,
      gas: serializableTransaction.gas,
      nonce,
      data: serializableTransaction.data
    };
    return [
      await account.signTypedData({
        domain: {
          name: gasless.domainName ?? "GSNv2 Forwarder",
          version: gasless.domainVersion ?? "0.0.1",
          chainId: transaction.chain.id,
          verifyingContract: forrwaderContract.address
        },
        message: message2,
        primaryType: "ForwardRequest",
        types: { ForwardRequest }
      }),
      message2
    ];
  })();
  const messageType = "forward";
  return { message, signature, messageType };
}
var ForwardRequest = [
  { name: "from", type: "address" },
  { name: "to", type: "address" },
  { name: "value", type: "uint256" },
  { name: "gas", type: "uint256" },
  { name: "nonce", type: "uint256" },
  { name: "data", type: "bytes" }
];
var ChainAwareForwardRequest = [
  { name: "from", type: "address" },
  { name: "to", type: "address" },
  { name: "value", type: "uint256" },
  { name: "gas", type: "uint256" },
  { name: "nonce", type: "uint256" },
  { name: "data", type: "bytes" },
  { name: "chainid", type: "uint256" }
];
async function relayOpenZeppelinTransaction(options) {
  const { message, messageType, signature } = await prepareOpenZeppelinTransaction(options);
  const response = await fetch(options.gasless.relayerUrl, {
    method: "POST",
    body: stringify({
      request: message,
      type: messageType,
      signature,
      forwarderAddress: options.gasless.relayerForwarderAddress
    })
  });
  if (!response.ok) {
    throw new Error(`Failed to send transaction: ${await response.text()}`);
  }
  const json = await response.json();
  if (!json.result) {
    throw new Error(`Relay transaction failed: ${json.message}`);
  }
  const transactionHash = JSON.parse(json.result).txHash;
  if (isHex(transactionHash)) {
    return {
      transactionHash,
      chain: options.transaction.chain,
      client: options.transaction.client
    };
  }
  throw new Error(`Failed to send transaction: ${stringify(json)}`);
}
export {
  prepareOpenZeppelinTransaction,
  relayOpenZeppelinTransaction
};
//# sourceMappingURL=openzeppelin-XP7CXYHJ.js.map
