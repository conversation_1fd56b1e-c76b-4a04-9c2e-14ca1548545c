{"version": 3, "sources": ["../../thirdweb/src/utils/signatures/sign-message.ts", "../../thirdweb/src/utils/signatures/sign-typed-data.ts", "../../thirdweb/src/wallets/private-key.ts", "../../thirdweb/src/wallets/utils/generateAccount.ts", "../../thirdweb/src/wallets/getAllWalletsList.ts", "../../thirdweb/src/adapters/wallet-adapter.ts", "../../thirdweb/src/adapters/eip1193/index.ts", "../../thirdweb/src/adapters/eip1193/from-eip1193.ts", "../../thirdweb/src/adapters/eip1193/to-eip1193.ts", "../../thirdweb/src/wallets/connection/autoConnect.ts"], "sourcesContent": ["import * as ox__Hex from \"ox/Hex\";\nimport * as ox__PersonalMessage from \"ox/PersonalMessage\";\nimport * as ox__Secp256k1 from \"ox/Secp256k1\";\nimport * as ox__Signature from \"ox/Signature\";\nimport type { Account } from \"../../wallets/interfaces/wallet.js\";\nimport type { Hex } from \"../encoding/hex.js\";\nimport type { Prettify } from \"../type-utils.js\";\n\ntype Message = Prettify<\n  | string\n  | {\n      raw: Hex | Uint8Array;\n    }\n>;\nexport type SignMessageOptions = {\n  message: Message;\n  privateKey: Hex;\n};\n\n/**\n * Signs a string message with a given private key.\n * @param options The options for signing.\n * @param options.message The message to be signed as a string or object containing raw hex or bytes\n * @param options.privateKey The private key to be used\n * @returns The signature as a hex string\n * @example\n * ```ts\n * import { signMessage } from \"thirdweb/utils\";\n * signMessage({\n *   message: \"Hello, world!\",\n *   privateKey: \"0x...\",\n * });\n * ```\n * @utils\n */\nexport function signMessage({ message, privateKey }: SignMessageOptions): Hex;\n\n/**\n * Signs a string message with a given account.\n * @param options The options for signing.\n * @param options.message The message to be signed as a string or object containing raw hex or bytes\n * @param options.account The account to be used\n * @returns The signature as a hex string\n * @example\n * ```ts\n * import { signMessage } from \"thirdweb/utils\";\n * await signMessage({\n *   message: \"Hello, world!\",\n *   account\n * });\n * ```\n * @walletUtils\n */\nexport function signMessage({\n  message,\n  account,\n}: { message: Message; account: Account }): Promise<Hex>;\n\nexport function signMessage(\n  options: SignMessageOptions | { message: Message; account: Account },\n): Hex | Promise<Hex> {\n  if (\"privateKey\" in options) {\n    const payload = ox__PersonalMessage.getSignPayload(\n      typeof options.message === \"object\"\n        ? options.message.raw\n        : ox__Hex.fromString(options.message),\n    );\n\n    const signature = ox__Secp256k1.sign({\n      payload,\n      privateKey: options.privateKey,\n    });\n    return ox__Signature.toHex(signature);\n  }\n  if (\"account\" in options) {\n    const { message, account } = options;\n    return account.signMessage({ message });\n  }\n  throw new Error(\"Either privateKey or account is required\");\n}\n", "import * as ox__Hex from \"ox/Hex\";\nimport * as ox__Secp256k1 from \"ox/Secp256k1\";\nimport * as ox__Signature from \"ox/Signature\";\nimport * as ox__TypedData from \"ox/TypedData\";\nimport type { Hex } from \"../encoding/hex.js\";\n\nexport type SignTypedDataOptions<\n  typedData extends\n    | ox__TypedData.TypedData\n    | Record<string, unknown> = ox__TypedData.TypedData,\n  primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n> = ox__TypedData.Definition<typedData, primaryType> & {\n  privateKey: Hex;\n};\n\n/**\n * Signs a typed data object with a given private key according to EIP712.\n * @param options The typed data is passed within options alongside the private key\n * @param options.privateKey The private key to sign the typed data with\n * @returns The signature as a hex string\n * @example\n * ```ts\n * import { signTypedData } from \"thirdweb/utils\";\n * signTypedData({\n *   privateKey: \"0x...\",\n *   ...typedData\n * });\n * ```\n * @utils\n */\nexport function signTypedData<\n  const typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | \"EIP712Domain\",\n>(options: SignTypedDataOptions<typedData, primaryType>): Hex {\n  const { privateKey, ...typedData } =\n    options as unknown as SignTypedDataOptions;\n\n  if (typeof typedData.domain?.chainId === \"string\") {\n    typedData.domain.chainId = ox__Hex.toNumber(typedData.domain.chainId);\n  }\n\n  const payload = ox__TypedData.getSignPayload(typedData);\n\n  const signature = ox__Secp256k1.sign({\n    payload,\n    privateKey,\n  });\n\n  return ox__Signature.toHex(signature);\n}\n", "import { secp256k1 } from \"@noble/curves/secp256k1\";\nimport * as ox__Authorization from \"ox/Authorization\";\nimport * as ox__Secp256k1 from \"ox/Secp256k1\";\nimport type * as ox__TypedData from \"ox/TypedData\";\nimport { publicKeyToAddress } from \"viem/utils\";\nimport { getCached<PERSON>hain } from \"../chains/utils.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport { eth_sendRawTransaction } from \"../rpc/actions/eth_sendRawTransaction.js\";\nimport { getRpcClient } from \"../rpc/rpc.js\";\nimport type { AuthorizationRequest } from \"../transaction/actions/eip7702/authorization.js\";\nimport { signTransaction } from \"../transaction/actions/sign-transaction.js\";\nimport type { SerializableTransaction } from \"../transaction/serialize-transaction.js\";\nimport { getAddress } from \"../utils/address.js\";\nimport { type Hex, toHex } from \"../utils/encoding/hex.js\";\nimport { signMessage } from \"../utils/signatures/sign-message.js\";\nimport { signTypedData } from \"../utils/signatures/sign-typed-data.js\";\nimport type { Prettify } from \"../utils/type-utils.js\";\nimport type { Account } from \"./interfaces/wallet.js\";\n\nexport type PrivateKeyToAccountOptions = {\n  /**\n   * A client is the entry point to the thirdweb SDK.\n   * It is required for all other actions.\n   * You can create a client using the `createThirdwebClient` function. Refer to the [Creating a Client](https://portal.thirdweb.com/typescript/v5/client) documentation for more information.\n   *\n   * You must provide a `clientId` or `secretKey` in order to initialize a client. Pass `clientId` if you want for client-side usage and `secretKey` for server-side usage.\n   *\n   * ```tsx\n   * import { createThirdwebClient } from \"thirdweb\";\n   *\n   * const client = createThirdwebClient({\n   *  clientId: \"<your_client_id>\",\n   * })\n   * ```\n   */\n  client: ThirdwebClient;\n\n  /**\n   * The private key to use for the account.\n   *\n   * Do not commit private key in your code and use environment variables or other secure methods to store the private key.\n   * @example\n   * ```ts\n   * const privateKey = process.env.PRIVATE_KEY;\n   * ```\n   */\n  privateKey: string;\n};\n\ntype Message = Prettify<\n  | string\n  | {\n      raw: Hex | Uint8Array;\n    }\n>;\n\n/**\n * Get an `Account` object from a private key.\n * @param options - The options for `privateKeyToAccount`\n * Refer to the type [`PrivateKeyToAccountOptions`](https://portal.thirdweb.com/references/typescript/v5/PrivateKeyToAccountOptions)\n * @returns The `Account` object that represents the private key\n * @example\n * ```ts\n * import { privateKeyToAccount } from \"thirdweb/wallets\"\n *\n * const wallet = privateKeyToAccount({\n *  client,\n *  privateKey: \"...\",\n * });\n * ```\n * @wallet\n */\nexport function privateKeyToAccount(\n  options: PrivateKeyToAccountOptions,\n): Account {\n  const { client } = options;\n  const privateKey = `0x${options.privateKey.replace(/^0x/, \"\")}` satisfies Hex;\n\n  const publicKey = toHex(secp256k1.getPublicKey(privateKey.slice(2), false));\n  const address = publicKeyToAddress(publicKey);\n\n  const account = {\n    address: getAddress(address),\n    sendTransaction: async (\n      tx: SerializableTransaction & { chainId: number },\n    ) => {\n      const rpcRequest = getRpcClient({\n        client: client,\n        chain: getCachedChain(tx.chainId),\n      });\n      const signedTx = signTransaction({\n        transaction: tx,\n        privateKey,\n      });\n      const transactionHash = await eth_sendRawTransaction(\n        rpcRequest,\n        signedTx,\n      );\n      return {\n        transactionHash,\n      };\n    },\n    signMessage: async ({ message }: { message: Message }) => {\n      return signMessage({\n        message,\n        privateKey,\n      });\n    },\n    signTypedData: async <\n      const typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n      primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n    >(\n      _typedData: ox__TypedData.Definition<typedData, primaryType>,\n    ) => {\n      return signTypedData({\n        ..._typedData,\n        privateKey,\n      });\n    },\n    signTransaction: async (tx: SerializableTransaction) => {\n      return signTransaction({\n        transaction: tx,\n        privateKey,\n      });\n    },\n    signAuthorization: async (authorization: AuthorizationRequest) => {\n      const signature = ox__Secp256k1.sign({\n        payload: ox__Authorization.getSignPayload(authorization),\n        privateKey: privateKey,\n      });\n      return ox__Authorization.from(authorization, { signature });\n    },\n  };\n\n  return account satisfies Account;\n}\n", "import { secp256k1 } from \"@noble/curves/secp256k1\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { toHex } from \"../../utils/encoding/hex.js\";\nimport type { Account } from \"../interfaces/wallet.js\";\nimport { privateKeyToAccount } from \"../private-key.js\";\n\nexport type GenerateAccountOptions = {\n  client: ThirdwebClient;\n};\n\n/**\n * Generates a new account with a random private key.\n * @param options - The options for generating the account.\n * @param options.client - The Thirdweb client to use for the generated account.\n * @returns A Thirdweb account.\n * @example\n * ```ts\n * import { generateAccount } from \"thirdweb/wallets\";\n * const account = await generateAccount({ client });\n * ```\n * @walletUtils\n */\nexport async function generateAccount(\n  options: GenerateAccountOptions,\n): Promise<Account> {\n  const privateKey = toHex(secp256k1.utils.randomPrivateKey());\n  return privateKeyToAccount({ privateKey, client: options.client });\n}\n", "/**\n * Hide it for documentation - but expose it because we will use this to render the list of wallets in docs website\n * Using dynamic import just to be extra safe and avoid any tree shaking issues\n * @internal\n */\nexport async function getAllWalletsList() {\n  return (await import(\"./__generated__/wallet-infos.js\")).default;\n}\n", "import type { Chain } from \"../chains/types.js\";\nimport { getCachedChainIfExists } from \"../chains/utils.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport type { Account, Wallet } from \"../wallets/interfaces/wallet.js\";\nimport { createWalletEmitter } from \"../wallets/wallet-emitter.js\";\n\nexport type AdapterWalletOptions = {\n  client: ThirdwebClient;\n  adaptedAccount: Account;\n  chain: Chain;\n  onDisconnect: () => Promise<void> | void;\n  switchChain: (chain: Chain) => Promise<void> | void;\n};\n\n/**\n * Creates a wallet from the given account.\n *\n * You can use this to:\n *\n * - convert a third party library wallet (wagmi, viem, ethers) into a thirdweb wallet.\n * - connect with a private key (for automated tests)\n *\n * Available wallet adatpers:\n * - [Viem](https://portal.thirdweb.com/references/typescript/v5/viemAdapter)\n * - [Ethers 6](https://portal.thirdweb.com/references/typescript/v5/ethers6Adapter)\n * - [Ethers 5](https://portal.thirdweb.com/references/typescript/v5/ethers5Adapter)\n *\n * @param options - The options for the adapter wallet.\n * @returns a wallet instance.\n *\n * @example\n * ```ts\n * import { createWalletAdapter } from \"thirdweb\";\n *\n * const wallet = createWalletAdapter({\n *  client,\n *  adaptedAccount,\n *  chain,\n *  onDisconnect: () => {\n *    // disconnect logic\n *  },\n *  switchChain: async (chain) => {\n *    // switch chain logic\n *  },\n * });\n * ```\n * @wallet\n */\nexport function createWalletAdapter(\n  options: AdapterWalletOptions,\n): Wallet<\"adapter\"> {\n  const emitter = createWalletEmitter<\"adapter\">();\n  let _chain = options.chain;\n  return {\n    id: \"adapter\",\n    subscribe: emitter.subscribe,\n    connect: async () => {\n      emitter.emit(\"onConnect\", options);\n      return options.adaptedAccount;\n    },\n    autoConnect: async () => {\n      emitter.emit(\"onConnect\", options);\n      return options.adaptedAccount;\n    },\n    disconnect: async () => {\n      await options.onDisconnect();\n      emitter.emit(\"disconnect\", undefined);\n    },\n    getAccount() {\n      return options.adaptedAccount;\n    },\n    getChain() {\n      const cachedChain = getCachedChainIfExists(_chain.id);\n      _chain = cachedChain || _chain;\n      return _chain;\n    },\n    getConfig() {\n      return options;\n    },\n    switchChain: async (chain) => {\n      await options.switchChain(chain);\n      _chain = chain;\n      emitter.emit(\"chainChanged\", chain);\n    },\n  };\n}\n", "export {\n  type FromEip1193AdapterOptions,\n  fromProvider,\n} from \"./from-eip1193.js\";\n\nexport {\n  type ToEip1193ProviderOptions,\n  toProvider,\n} from \"./to-eip1193.js\";\n\nexport type { EIP1193Provider } from \"./types.js\";\n", "import * as ox__Hex from \"ox/Hex\";\nimport { trackConnect } from \"../../analytics/track/connect.js\";\nimport type { Chain } from \"../../chains/types.js\";\nimport { getCachedChainIfExists } from \"../../chains/utils.js\";\nimport {\n  autoConnectEip1193Wallet,\n  connectEip1193Wallet,\n} from \"../../wallets/injected/index.js\";\nimport type { Account, Wallet } from \"../../wallets/interfaces/wallet.js\";\nimport { createWalletEmitter } from \"../../wallets/wallet-emitter.js\";\nimport type { WalletId } from \"../../wallets/wallet-types.js\";\nimport type { EIP1193Provider } from \"./types.js\";\n\n/**\n * Options for creating an EIP-1193 provider adapter.\n */\nexport type FromEip1193AdapterOptions = {\n  provider:\n    | EIP1193Provider\n    | ((params?: { chainId?: number }) => Promise<EIP1193Provider>);\n  walletId?: WalletId;\n};\n\n/**\n * Creates a Thirdweb wallet from an EIP-1193 compatible provider.\n *\n * This adapter allows you to use any EIP-1193 provider (like MetaMask, WalletConnect, etc.) as a Thirdweb wallet.\n * It handles all the necessary conversions between the EIP-1193 interface and Thirdweb's wallet interface.\n *\n * @param options - Configuration options for creating the wallet adapter\n * @param options.provider - An EIP-1193 compatible provider or a function that returns one\n * @param options.walletId - Optional custom wallet ID to identify this provider (defaults to \"adapter\")\n * @returns A Thirdweb wallet instance that wraps the EIP-1193 provider\n *\n * @example\n * ```ts\n * import { EIP1193 } from \"thirdweb/wallets\";\n *\n * // Create a Thirdweb wallet from MetaMask's provider\n * const wallet = EIP1193.fromProvider({\n *   provider: window.ethereum,\n *   walletId: \"io.metamask\"\n * });\n *\n * // Use like any other Thirdweb wallet\n * const account = await wallet.connect({\n *   client: createThirdwebClient({ clientId: \"...\" })\n * });\n *\n * // Sign messages\n * await account.signMessage({ message: \"Hello World\" });\n *\n * // Send transactions\n * await account.sendTransaction({\n *   to: \"0x...\",\n *   value: 1000000000000000000n\n * });\n * ```\n *\n * @extension EIP1193\n */\nexport function fromProvider(options: FromEip1193AdapterOptions): Wallet {\n  const id: WalletId = options.walletId ?? \"adapter\";\n  const emitter = createWalletEmitter();\n  let account: Account | undefined = undefined;\n  let chain: Chain | undefined = undefined;\n  let provider: EIP1193Provider | undefined = undefined;\n  const getProvider = async (params?: { chainId?: number }) => {\n    provider =\n      typeof options.provider === \"function\"\n        ? await options.provider(params)\n        : options.provider;\n    return provider;\n  };\n\n  const unsubscribeChain = emitter.subscribe(\"chainChanged\", (newChain) => {\n    chain = newChain;\n  });\n\n  function reset() {\n    account = undefined;\n    chain = undefined;\n  }\n\n  let handleDisconnect = async () => {};\n\n  const unsubscribeDisconnect = emitter.subscribe(\"disconnect\", () => {\n    reset();\n    unsubscribeChain();\n    unsubscribeDisconnect();\n  });\n\n  emitter.subscribe(\"accountChanged\", (_account) => {\n    account = _account;\n  });\n\n  let handleSwitchChain: (c: Chain) => Promise<void> = async (c) => {\n    await provider?.request({\n      method: \"wallet_switchEthereumChain\",\n      params: [{ chainId: ox__Hex.fromNumber(c.id) }],\n    });\n  };\n\n  return {\n    id,\n    subscribe: emitter.subscribe,\n    getConfig: () => undefined,\n    getChain() {\n      if (!chain) {\n        return undefined;\n      }\n\n      chain = getCachedChainIfExists(chain.id) || chain;\n      return chain;\n    },\n    getAccount: () => account,\n    connect: async (connectOptions) => {\n      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] =\n        await connectEip1193Wallet({\n          id,\n          provider: await getProvider({ chainId: connectOptions.chain?.id }),\n          client: connectOptions.client,\n          chain: connectOptions.chain,\n          emitter,\n        });\n      // set the states\n      account = connectedAccount;\n      chain = connectedChain;\n      handleDisconnect = doDisconnect;\n      handleSwitchChain = doSwitchChain;\n      emitter.emit(\"onConnect\", connectOptions);\n      trackConnect({\n        client: connectOptions.client,\n        walletType: id,\n        walletAddress: account.address,\n      });\n      // return account\n      return account;\n    },\n    autoConnect: async (connectOptions) => {\n      const [connectedAccount, connectedChain, doDisconnect, doSwitchChain] =\n        await autoConnectEip1193Wallet({\n          id,\n          provider: await getProvider({ chainId: connectOptions.chain?.id }),\n          emitter,\n          chain: connectOptions.chain,\n          client: connectOptions.client,\n        });\n      // set the states\n      account = connectedAccount;\n      chain = connectedChain;\n      handleDisconnect = doDisconnect;\n      handleSwitchChain = doSwitchChain;\n      emitter.emit(\"onConnect\", connectOptions);\n      trackConnect({\n        client: connectOptions.client,\n        walletType: id,\n        walletAddress: account.address,\n      });\n      // return account\n      return account;\n    },\n    disconnect: async () => {\n      reset();\n      await handleDisconnect();\n      emitter.emit(\"disconnect\", undefined);\n    },\n    switchChain: async (c) => {\n      await handleSwitchChain(c);\n      emitter.emit(\"chainChanged\", c);\n    },\n  };\n}\n", "import type { Account } from \"viem/accounts\";\n\nimport type { Chain } from \"../../chains/types.js\";\nimport { getCached<PERSON><PERSON><PERSON> } from \"../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { getRpcClient } from \"../../rpc/rpc.js\";\nimport { estimateGas } from \"../../transaction/actions/estimate-gas.js\";\nimport { sendTransaction } from \"../../transaction/actions/send-transaction.js\";\nimport { prepareTransaction } from \"../../transaction/prepare-transaction.js\";\nimport { hexToNumber, isHex } from \"../../utils/encoding/hex.js\";\nimport type { Wallet } from \"../../wallets/interfaces/wallet.js\";\nimport type { EIP1193Provider } from \"./types.js\";\n\nexport type ToEip1193ProviderOptions = {\n  wallet: Wallet;\n  chain: Chain;\n  client: ThirdwebClient;\n  connectOverride?: (wallet: Wallet) => Promise<Account>;\n};\n\n/**\n * Converts a Thirdweb wallet into an EIP-1193 compatible provider.\n *\n * This adapter allows you to use a Thirdweb wallet with any library or dApp that expects an EIP-1193 provider.\n * The provider implements the standard EIP-1193 interface including request handling and event subscription.\n *\n * @param options - Configuration options for creating the provider\n * @param options.wallet - The Thirdweb wallet to adapt into a provider\n * @param options.chain - The blockchain chain to connect to\n * @param options.client - The Thirdweb client instance\n * @param options.connectOverride - Optional custom connect handler to override default connection behavior\n * @returns An EIP-1193 compatible provider that wraps the Thirdweb wallet\n *\n * @example\n * ```ts\n * import { EIP1193 } from \"thirdweb/wallets\";\n *\n * // Create an EIP-1193 provider from a Thirdweb wallet\n * const provider = EIP1193.toProvider({\n *   wallet,\n *   chain: ethereum,\n *   client: createThirdwebClient({ clientId: \"...\" })\n * });\n *\n * // Use with any EIP-1193 compatible library\n * const accounts = await provider.request({\n *   method: \"eth_requestAccounts\"\n * });\n *\n * // Listen for events\n * provider.on(\"accountsChanged\", (accounts) => {\n *   console.log(\"Active accounts:\", accounts);\n * });\n * ```\n *\n * @extension EIP1193\n */\nexport function toProvider(options: ToEip1193ProviderOptions): EIP1193Provider {\n  const { chain, client, wallet, connectOverride } = options;\n  const rpcClient = getRpcClient({ client, chain });\n  return {\n    on: wallet.subscribe,\n    removeListener: () => {\n      // should invoke the return fn from subscribe instead\n    },\n    request: async (request) => {\n      if (request.method === \"eth_sendTransaction\") {\n        const account = wallet.getAccount();\n        if (!account) {\n          throw new Error(\"Account not connected\");\n        }\n        const result = await sendTransaction({\n          transaction: prepareTransaction({\n            ...request.params[0],\n            chain,\n            client,\n          }),\n          account: account,\n        });\n        return result.transactionHash;\n      }\n      if (request.method === \"eth_estimateGas\") {\n        const account = wallet.getAccount();\n        if (!account) {\n          throw new Error(\"Account not connected\");\n        }\n        return estimateGas({\n          transaction: prepareTransaction({\n            ...request.params[0],\n            chain,\n            client,\n          }),\n          account,\n        });\n      }\n      if (request.method === \"personal_sign\") {\n        const account = wallet.getAccount();\n        if (!account) {\n          throw new Error(\"Account not connected\");\n        }\n        return account.signMessage({\n          message: {\n            raw: request.params[0],\n          },\n        });\n      }\n      if (request.method === \"eth_signTypedData_v4\") {\n        const account = wallet.getAccount();\n        if (!account) {\n          throw new Error(\"Account not connected\");\n        }\n        const data = JSON.parse(request.params[1]);\n        return account.signTypedData(data);\n      }\n      if (request.method === \"eth_accounts\") {\n        const account = wallet.getAccount();\n        if (!account) {\n          throw new Error(\"Account not connected\");\n        }\n        return [account.address];\n      }\n      if (request.method === \"eth_requestAccounts\") {\n        const account = connectOverride\n          ? await connectOverride(wallet)\n          : await wallet.connect({\n              client,\n            });\n        if (!account) {\n          throw new Error(\"Unable to connect wallet\");\n        }\n        return [account.address];\n      }\n      if (\n        request.method === \"wallet_switchEthereumChain\" ||\n        request.method === \"wallet_addEthereumChain\"\n      ) {\n        const data = request.params[0];\n        const chainIdHex = data.chainId;\n        if (!chainIdHex) {\n          throw new Error(\"Chain ID is required\");\n        }\n        // chainId is hex most likely, convert to number\n        const chainId = isHex(chainIdHex)\n          ? hexToNumber(chainIdHex)\n          : chainIdHex;\n        const chain = getCachedChain(chainId);\n        return wallet.switchChain(chain);\n      }\n      return rpcClient(request);\n    },\n  };\n}\n", "import { webLocalStorage } from \"../../utils/storage/webStorage.js\";\nimport { createWallet } from \"../create-wallet.js\";\nimport { getDefaultWallets } from \"../defaultWallets.js\";\nimport { getInstalledWalletProviders } from \"../injected/mipdStore.js\";\nimport type { Wallet } from \"../interfaces/wallet.js\";\nimport { createConnectionManager } from \"../manager/index.js\";\nimport { autoConnectCore } from \"./autoConnectCore.js\";\nimport type { AutoConnectProps } from \"./types.js\";\n\n/**\n * Attempts to automatically connect to the last connected wallet.\n * It combines both specified wallets and installed wallet providers that aren't already specified.\n *\n * @example\n *\n * ```tsx\n * import { autoConnect } from \"thirdweb/wallets\";\n *\n * const autoConnected = await autoConnect({\n *  client,\n *  onConnect: (wallet) => {\n *    console.log(\"wallet\", wallet);\n *  },\n * });\n * ```\n *\n * @param props - The auto-connect configuration properties\n * @param props.wallets - Array of wallet instances to consider for auto-connection\n * @returns {boolean} a promise resolving to true or false depending on whether the auto connect function connected to a wallet or not\n * @walletConnection\n */\nexport async function autoConnect(\n  props: AutoConnectProps & {\n    wallets?: Wallet[];\n    /**\n     * If true, the auto connect will be forced even if autoConnect has already been attempted successfully earlier.\n     *\n     * @default `false`\n     */\n    force?: boolean;\n  },\n): Promise<boolean> {\n  const wallets = props.wallets || getDefaultWallets(props);\n  const manager = createConnectionManager(webLocalStorage);\n  const result = await autoConnectCore({\n    storage: webLocalStorage,\n    props: {\n      ...props,\n      wallets,\n    },\n    createWalletFn: createWallet,\n    getInstalledWallets: () => {\n      const specifiedWalletIds = new Set(wallets.map((x) => x.id));\n\n      // pass the wallets that are not already specified but are installed by the user\n      const installedWallets = getInstalledWalletProviders()\n        .filter((x) => !specifiedWalletIds.has(x.info.rdns))\n        .map((x) => createWallet(x.info.rdns));\n\n      return installedWallets;\n    },\n    manager,\n  });\n  return result;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DM,SAAU,YACd,SAAoE;AAEpE,MAAI,gBAAgB,SAAS;AAC3B,UAAM,UAA8BA,gBAClC,OAAO,QAAQ,YAAY,WACvB,QAAQ,QAAQ,MACR,WAAW,QAAQ,OAAO,CAAC;AAGzC,UAAM,YAA0B,KAAK;MACnC;MACA,YAAY,QAAQ;KACrB;AACD,WAAqBC,OAAM,SAAS;EACtC;AACA,MAAI,aAAa,SAAS;AACxB,UAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,WAAO,QAAQ,YAAY,EAAE,QAAO,CAAE;EACxC;AACA,QAAM,IAAI,MAAM,0CAA0C;AAC5D;;;ACjDM,SAAU,cAGd,SAAqD;AAjCvD;AAkCE,QAAM,EAAE,YAAY,GAAG,UAAS,IAC9B;AAEF,MAAI,SAAO,eAAU,WAAV,mBAAkB,aAAY,UAAU;AACjD,cAAU,OAAO,UAAkB,SAAS,UAAU,OAAO,OAAO;EACtE;AAEA,QAAM,UAAwBC,gBAAe,SAAS;AAEtD,QAAM,YAA0B,KAAK;IACnC;IACA;GACD;AAED,SAAqBC,OAAM,SAAS;AACtC;;;ACuBM,SAAU,oBACd,SAAmC;AAEnC,QAAM,EAAE,OAAM,IAAK;AACnB,QAAM,aAAa,KAAK,QAAQ,WAAW,QAAQ,OAAO,EAAE,CAAC;AAE7D,QAAM,YAAY,MAAM,UAAU,aAAa,WAAW,MAAM,CAAC,GAAG,KAAK,CAAC;AAC1E,QAAM,UAAU,mBAAmB,SAAS;AAE5C,QAAM,UAAU;IACd,SAAS,WAAW,OAAO;IAC3B,iBAAiB,OACf,OACE;AACF,YAAM,aAAa,aAAa;QAC9B;QACA,OAAO,eAAe,GAAG,OAAO;OACjC;AACD,YAAM,WAAW,gBAAgB;QAC/B,aAAa;QACb;OACD;AACD,YAAM,kBAAkB,MAAM,uBAC5B,YACA,QAAQ;AAEV,aAAO;QACL;;IAEJ;IACA,aAAa,OAAO,EAAE,QAAO,MAA4B;AACvD,aAAO,YAAY;QACjB;QACA;OACD;IACH;IACA,eAAe,OAIb,eACE;AACF,aAAO,cAAc;QACnB,GAAG;QACH;OACD;IACH;IACA,iBAAiB,OAAO,OAA+B;AACrD,aAAO,gBAAgB;QACrB,aAAa;QACb;OACD;IACH;IACA,mBAAmB,OAAO,kBAAuC;AAC/D,YAAM,YAA0B,KAAK;QACnC,SAA2B,eAAe,aAAa;QACvD;OACD;AACD,aAAyB,KAAK,eAAe,EAAE,UAAS,CAAE;IAC5D;;AAGF,SAAO;AACT;;;ACjHA,eAAsB,gBACpB,SAA+B;AAE/B,QAAM,aAAa,MAAM,UAAU,MAAM,iBAAgB,CAAE;AAC3D,SAAO,oBAAoB,EAAE,YAAY,QAAQ,QAAQ,OAAM,CAAE;AACnE;;;ACtBA,eAAsB,oBAAiB;AACrC,UAAQ,MAAM,OAAO,4BAAiC,GAAG;AAC3D;;;ACyCM,SAAU,oBACd,SAA6B;AAE7B,QAAM,UAAU,oBAAmB;AACnC,MAAI,SAAS,QAAQ;AACrB,SAAO;IACL,IAAI;IACJ,WAAW,QAAQ;IACnB,SAAS,YAAW;AAClB,cAAQ,KAAK,aAAa,OAAO;AACjC,aAAO,QAAQ;IACjB;IACA,aAAa,YAAW;AACtB,cAAQ,KAAK,aAAa,OAAO;AACjC,aAAO,QAAQ;IACjB;IACA,YAAY,YAAW;AACrB,YAAM,QAAQ,aAAY;AAC1B,cAAQ,KAAK,cAAc,MAAS;IACtC;IACA,aAAU;AACR,aAAO,QAAQ;IACjB;IACA,WAAQ;AACN,YAAM,cAAc,uBAAuB,OAAO,EAAE;AACpD,eAAS,eAAe;AACxB,aAAO;IACT;IACA,YAAS;AACP,aAAO;IACT;IACA,aAAa,OAAO,UAAS;AAC3B,YAAM,QAAQ,YAAY,KAAK;AAC/B,eAAS;AACT,cAAQ,KAAK,gBAAgB,KAAK;IACpC;;AAEJ;;;ACrFA;;;;;;;AC6DM,SAAU,aAAa,SAAkC;AAC7D,QAAM,KAAe,QAAQ,YAAY;AACzC,QAAM,UAAU,oBAAmB;AACnC,MAAI,UAA+B;AACnC,MAAI,QAA2B;AAC/B,MAAI,WAAwC;AAC5C,QAAM,cAAc,OAAO,WAAiC;AAC1D,eACE,OAAO,QAAQ,aAAa,aACxB,MAAM,QAAQ,SAAS,MAAM,IAC7B,QAAQ;AACd,WAAO;EACT;AAEA,QAAM,mBAAmB,QAAQ,UAAU,gBAAgB,CAAC,aAAY;AACtE,YAAQ;EACV,CAAC;AAED,WAAS,QAAK;AACZ,cAAU;AACV,YAAQ;EACV;AAEA,MAAI,mBAAmB,YAAW;EAAE;AAEpC,QAAM,wBAAwB,QAAQ,UAAU,cAAc,MAAK;AACjE,UAAK;AACL,qBAAgB;AAChB,0BAAqB;EACvB,CAAC;AAED,UAAQ,UAAU,kBAAkB,CAAC,aAAY;AAC/C,cAAU;EACZ,CAAC;AAED,MAAI,oBAAiD,OAAO,MAAK;AAC/D,WAAM,qCAAU,QAAQ;MACtB,QAAQ;MACR,QAAQ,CAAC,EAAE,SAAiB,WAAW,EAAE,EAAE,EAAC,CAAE;;EAElD;AAEA,SAAO;IACL;IACA,WAAW,QAAQ;IACnB,WAAW,MAAM;IACjB,WAAQ;AACN,UAAI,CAAC,OAAO;AACV,eAAO;MACT;AAEA,cAAQ,uBAAuB,MAAM,EAAE,KAAK;AAC5C,aAAO;IACT;IACA,YAAY,MAAM;IAClB,SAAS,OAAO,mBAAkB;AApHtC;AAqHM,YAAM,CAAC,kBAAkB,gBAAgB,cAAc,aAAa,IAClE,MAAM,qBAAqB;QACzB;QACA,UAAU,MAAM,YAAY,EAAE,UAAS,oBAAe,UAAf,mBAAsB,GAAE,CAAE;QACjE,QAAQ,eAAe;QACvB,OAAO,eAAe;QACtB;OACD;AAEH,gBAAU;AACV,cAAQ;AACR,yBAAmB;AACnB,0BAAoB;AACpB,cAAQ,KAAK,aAAa,cAAc;AACxC,mBAAa;QACX,QAAQ,eAAe;QACvB,YAAY;QACZ,eAAe,QAAQ;OACxB;AAED,aAAO;IACT;IACA,aAAa,OAAO,mBAAkB;AA3I1C;AA4IM,YAAM,CAAC,kBAAkB,gBAAgB,cAAc,aAAa,IAClE,MAAM,yBAAyB;QAC7B;QACA,UAAU,MAAM,YAAY,EAAE,UAAS,oBAAe,UAAf,mBAAsB,GAAE,CAAE;QACjE;QACA,OAAO,eAAe;QACtB,QAAQ,eAAe;OACxB;AAEH,gBAAU;AACV,cAAQ;AACR,yBAAmB;AACnB,0BAAoB;AACpB,cAAQ,KAAK,aAAa,cAAc;AACxC,mBAAa;QACX,QAAQ,eAAe;QACvB,YAAY;QACZ,eAAe,QAAQ;OACxB;AAED,aAAO;IACT;IACA,YAAY,YAAW;AACrB,YAAK;AACL,YAAM,iBAAgB;AACtB,cAAQ,KAAK,cAAc,MAAS;IACtC;IACA,aAAa,OAAO,MAAK;AACvB,YAAM,kBAAkB,CAAC;AACzB,cAAQ,KAAK,gBAAgB,CAAC;IAChC;;AAEJ;;;ACnHM,SAAU,WAAW,SAAiC;AAC1D,QAAM,EAAE,OAAO,QAAQ,QAAQ,gBAAe,IAAK;AACnD,QAAM,YAAY,aAAa,EAAE,QAAQ,MAAK,CAAE;AAChD,SAAO;IACL,IAAI,OAAO;IACX,gBAAgB,MAAK;IAErB;IACA,SAAS,OAAO,YAAW;AACzB,UAAI,QAAQ,WAAW,uBAAuB;AAC5C,cAAM,UAAU,OAAO,WAAU;AACjC,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,uBAAuB;QACzC;AACA,cAAM,SAAS,MAAM,gBAAgB;UACnC,aAAa,mBAAmB;YAC9B,GAAG,QAAQ,OAAO,CAAC;YACnB;YACA;WACD;UACD;SACD;AACD,eAAO,OAAO;MAChB;AACA,UAAI,QAAQ,WAAW,mBAAmB;AACxC,cAAM,UAAU,OAAO,WAAU;AACjC,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,uBAAuB;QACzC;AACA,eAAO,YAAY;UACjB,aAAa,mBAAmB;YAC9B,GAAG,QAAQ,OAAO,CAAC;YACnB;YACA;WACD;UACD;SACD;MACH;AACA,UAAI,QAAQ,WAAW,iBAAiB;AACtC,cAAM,UAAU,OAAO,WAAU;AACjC,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,uBAAuB;QACzC;AACA,eAAO,QAAQ,YAAY;UACzB,SAAS;YACP,KAAK,QAAQ,OAAO,CAAC;;SAExB;MACH;AACA,UAAI,QAAQ,WAAW,wBAAwB;AAC7C,cAAM,UAAU,OAAO,WAAU;AACjC,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,uBAAuB;QACzC;AACA,cAAM,OAAO,KAAK,MAAM,QAAQ,OAAO,CAAC,CAAC;AACzC,eAAO,QAAQ,cAAc,IAAI;MACnC;AACA,UAAI,QAAQ,WAAW,gBAAgB;AACrC,cAAM,UAAU,OAAO,WAAU;AACjC,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,uBAAuB;QACzC;AACA,eAAO,CAAC,QAAQ,OAAO;MACzB;AACA,UAAI,QAAQ,WAAW,uBAAuB;AAC5C,cAAM,UAAU,kBACZ,MAAM,gBAAgB,MAAM,IAC5B,MAAM,OAAO,QAAQ;UACnB;SACD;AACL,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,0BAA0B;QAC5C;AACA,eAAO,CAAC,QAAQ,OAAO;MACzB;AACA,UACE,QAAQ,WAAW,gCACnB,QAAQ,WAAW,2BACnB;AACA,cAAM,OAAO,QAAQ,OAAO,CAAC;AAC7B,cAAM,aAAa,KAAK;AACxB,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM,sBAAsB;QACxC;AAEA,cAAM,UAAU,MAAM,UAAU,IAC5B,YAAY,UAAU,IACtB;AACJ,cAAMC,SAAQ,eAAe,OAAO;AACpC,eAAO,OAAO,YAAYA,MAAK;MACjC;AACA,aAAO,UAAU,OAAO;IAC1B;;AAEJ;;;ACxHA,eAAsB,YACpB,OAQC;AAED,QAAM,UAAU,MAAM,WAAW,kBAAkB,KAAK;AACxD,QAAM,UAAU,wBAAwB,eAAe;AACvD,QAAM,SAAS,MAAM,gBAAgB;IACnC,SAAS;IACT,OAAO;MACL,GAAG;MACH;;IAEF,gBAAgB;IAChB,qBAAqB,MAAK;AACxB,YAAM,qBAAqB,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AAG3D,YAAM,mBAAmB,4BAA2B,EACjD,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,EAAE,KAAK,IAAI,CAAC,EAClD,IAAI,CAAC,MAAM,aAAa,EAAE,KAAK,IAAI,CAAC;AAEvC,aAAO;IACT;IACA;GACD;AACD,SAAO;AACT;", "names": ["getSignPayload", "toHex", "getSignPayload", "toHex", "chain"]}