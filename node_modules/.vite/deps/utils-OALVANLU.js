import {
  CUSTOM_CHAIN_MAP,
  cacheChains,
  convertApiChainToChain,
  convertLegacyChain,
  convertViemChain,
  define<PERSON>hain,
  getCachedChain,
  getCachedChainIfExists,
  getChainDecimals,
  getChainMetadata,
  getChainNativeCurrencyName,
  getChainServices,
  getChainSymbol,
  getRpcUrlForChain
} from "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";
export {
  CUSTOM_CHAIN_MAP,
  cacheChains,
  convertApiChainToChain,
  convertLegacyChain,
  convertViemChain,
  define<PERSON>hain,
  getCached<PERSON>hain,
  getCachedChainIfExists,
  getChainDecimals,
  getChainMetadata,
  getChainNativeCurrencyName,
  getChainServices,
  getChainSymbol,
  getRpcUrlForChain
};
//# sourceMappingURL=utils-OALVANLU.js.map
