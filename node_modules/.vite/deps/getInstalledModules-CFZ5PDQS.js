import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  readContract
} from "./chunk-A2MKHEYJ.js";
import "./chunk-TVIYKLDL.js";
import "./chunk-JOYHE2MS.js";
import "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import {
  decodeAbiParameters
} from "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/modules/__generated__/IModularCore/read/getInstalledModules.js
var FN_SELECTOR = "0x3e429396";
var FN_INPUTS = [];
var FN_OUTPUTS = [
  {
    type: "tuple[]",
    components: [
      {
        type: "address",
        name: "implementation"
      },
      {
        type: "tuple",
        name: "config",
        components: [
          {
            type: "bool",
            name: "registerInstallationCallback"
          },
          {
            type: "bytes4[]",
            name: "requiredInterfaces"
          },
          {
            type: "bytes4[]",
            name: "supportedInterfaces"
          },
          {
            type: "tuple[]",
            name: "callbackFunctions",
            components: [
              {
                type: "bytes4",
                name: "selector"
              }
            ]
          },
          {
            type: "tuple[]",
            name: "fallbackFunctions",
            components: [
              {
                type: "bytes4",
                name: "selector"
              },
              {
                type: "uint256",
                name: "permissionBits"
              }
            ]
          }
        ]
      }
    ]
  }
];
function isGetInstalledModulesSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
function decodeGetInstalledModulesResult(result) {
  return decodeAbiParameters(FN_OUTPUTS, result)[0];
}
async function getInstalledModules(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: []
  });
}
export {
  FN_SELECTOR,
  decodeGetInstalledModulesResult,
  getInstalledModules,
  isGetInstalledModulesSupported
};
//# sourceMappingURL=getInstalledModules-CFZ5PDQS.js.map
