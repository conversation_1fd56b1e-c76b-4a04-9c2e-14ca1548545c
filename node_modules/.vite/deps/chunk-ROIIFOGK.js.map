{"version": 3, "sources": ["../../thirdweb/src/utils/extensions/drops/get-claim-params.ts"], "sourcesContent": ["import { maxUint256, padHex } from \"viem\";\nimport {\n  ZERO_ADDRESS,\n  isNativeTokenAddress,\n} from \"../../../constants/addresses.js\";\nimport type { ThirdwebContract } from \"../../../contract/contract.js\";\nimport { getContractMetadata } from \"../../../extensions/common/read/getContractMetadata.js\";\nimport type { Hex } from \"../../encoding/hex.js\";\nimport type { ClaimCondition, OverrideProof } from \"./types.js\";\n\nexport type GetClaimParamsOptions = {\n  contract: ThirdwebContract;\n  to: string;\n  quantity: bigint;\n  from?: string;\n  singlePhaseDrop?: boolean;\n} & (\n  | {\n      type: \"erc721\";\n    }\n  | {\n      type: \"erc20\";\n      tokenDecimals: number;\n    }\n  | {\n      type: \"erc1155\";\n      tokenId: bigint;\n    }\n);\n\n/**\n * Get the claim parameters for a given drop\n * @param options - The options for getting the claim parameters\n * @returns The claim parameters\n * @example\n * ```ts\n * import { getClaimParams } from \"thirdweb/utils\";\n *\n * const claimParams = await getClaimParams({\n *  contract,\n *  to: \"0x...\",\n *  quantity: 1n,\n *  type: \"erc1155\",\n *  tokenId: 0n,\n * });\n * ```\n * @utils\n */\nexport async function getClaimParams(options: GetClaimParamsOptions) {\n  const cc: ClaimCondition = await (async () => {\n    if (options.type === \"erc1155\") {\n      // lazy-load the getActiveClaimCondition function\n      const { getActiveClaimCondition } = await import(\n        \"../../../extensions/erc1155/drops/read/getActiveClaimCondition.js\"\n      );\n      return await getActiveClaimCondition({\n        contract: options.contract,\n        tokenId: options.tokenId,\n      });\n    }\n    if (options.type === \"erc721\") {\n      // lazy-load the getActiveClaimCondition function\n      if (options.singlePhaseDrop) {\n        const { claimCondition } = await import(\n          \"../../../extensions/erc721/__generated__/IDropSinglePhase/read/claimCondition.js\"\n        );\n        return await claimCondition({\n          contract: options.contract,\n        });\n      }\n      const { getActiveClaimCondition } = await import(\n        \"../../../extensions/erc721/drops/read/getActiveClaimCondition.js\"\n      );\n      return await getActiveClaimCondition({\n        contract: options.contract,\n      });\n    }\n\n    // otherwise erc20 case!\n\n    // lazy-load the getActiveClaimCondition function\n    if (options.singlePhaseDrop) {\n      // same ABI as erc721\n      const { claimCondition } = await import(\n        \"../../../extensions/erc721/__generated__/IDropSinglePhase/read/claimCondition.js\"\n      );\n      return await claimCondition({\n        contract: options.contract,\n      });\n    }\n    const { getActiveClaimCondition } = await import(\n      \"../../../extensions/erc20/drops/read/getActiveClaimCondition.js\"\n    );\n    return await getActiveClaimCondition({\n      contract: options.contract,\n    });\n  })();\n\n  const tokenDecimals = options.type === \"erc20\" ? options.tokenDecimals : 0; // nfts have no decimals\n\n  // compute the allowListProof in an iife\n  const allowlistProof = await (async () => {\n    // early exit if no merkle root is set\n    if (!cc.merkleRoot || cc.merkleRoot === padHex(\"0x\", { size: 32 })) {\n      return {\n        currency: ZERO_ADDRESS,\n        proof: [],\n        quantityLimitPerWallet: 0n,\n        pricePerToken: maxUint256,\n      } satisfies OverrideProof;\n    }\n    // lazy-load the fetchProofsForClaimer function if we need it\n    const { fetchProofsForClaimer } = await import(\n      \"./fetch-proofs-for-claimers.js\"\n    );\n\n    // 1. fetch merkle data from contract URI\n    const metadata = await getContractMetadata({\n      contract: options.contract,\n    });\n    const merkleData: Record<string, string> = metadata.merkle || {};\n    const snapshotUri = merkleData[cc.merkleRoot];\n\n    if (!snapshotUri) {\n      return {\n        currency: ZERO_ADDRESS,\n        proof: [],\n        quantityLimitPerWallet: 0n,\n        pricePerToken: maxUint256,\n      } satisfies OverrideProof;\n    }\n\n    const allowListProof = await fetchProofsForClaimer({\n      contract: options.contract,\n      claimer: options.from || options.to, // receiver and claimer can be different, always prioritize the claimer for allowlists\n      merkleTreeUri: snapshotUri,\n      tokenDecimals,\n    });\n    // if no proof is found, we'll try the empty proof\n    if (!allowListProof) {\n      return {\n        currency: ZERO_ADDRESS,\n        proof: [],\n        quantityLimitPerWallet: 0n,\n        pricePerToken: maxUint256,\n      } satisfies OverrideProof;\n    }\n    // otherwise return the proof\n    return allowListProof;\n  })();\n\n  // currency and price need to match the allowlist proof if set\n  // if default values in the allowlist proof, fallback to the claim condition\n  const currency =\n    allowlistProof.currency && allowlistProof.currency !== ZERO_ADDRESS\n      ? allowlistProof.currency\n      : cc.currency;\n  const pricePerToken =\n    allowlistProof.pricePerToken !== undefined &&\n    allowlistProof.pricePerToken !== maxUint256\n      ? allowlistProof.pricePerToken\n      : cc.pricePerToken;\n\n  const totalPrice =\n    (pricePerToken * options.quantity) / BigInt(10 ** tokenDecimals);\n  const value = isNativeTokenAddress(currency) ? totalPrice : 0n;\n  const erc20Value =\n    !isNativeTokenAddress(currency) && pricePerToken > 0n\n      ? {\n          amountWei: totalPrice,\n          tokenAddress: currency,\n        }\n      : undefined;\n\n  return {\n    receiver: options.to,\n    tokenId: options.type === \"erc1155\" ? options.tokenId : undefined,\n    quantity: options.quantity,\n    currency,\n    pricePerToken,\n    allowlistProof,\n    data: \"0x\" as Hex,\n    overrides: {\n      value,\n      erc20Value,\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;AAgDA,eAAsB,eAAe,SAA8B;AACjE,QAAM,KAAqB,OAAO,YAAW;AAC3C,QAAI,QAAQ,SAAS,WAAW;AAE9B,YAAM,EAAE,yBAAAA,yBAAuB,IAAK,MAAM,OACxC,uCAAmE;AAErE,aAAO,MAAMA,yBAAwB;QACnC,UAAU,QAAQ;QAClB,SAAS,QAAQ;OAClB;IACH;AACA,QAAI,QAAQ,SAAS,UAAU;AAE7B,UAAI,QAAQ,iBAAiB;AAC3B,cAAM,EAAE,eAAc,IAAK,MAAM,OAC/B,8BAAkF;AAEpF,eAAO,MAAM,eAAe;UAC1B,UAAU,QAAQ;SACnB;MACH;AACA,YAAM,EAAE,yBAAAA,yBAAuB,IAAK,MAAM,OACxC,uCAAkE;AAEpE,aAAO,MAAMA,yBAAwB;QACnC,UAAU,QAAQ;OACnB;IACH;AAKA,QAAI,QAAQ,iBAAiB;AAE3B,YAAM,EAAE,eAAc,IAAK,MAAM,OAC/B,8BAAkF;AAEpF,aAAO,MAAM,eAAe;QAC1B,UAAU,QAAQ;OACnB;IACH;AACA,UAAM,EAAE,wBAAuB,IAAK,MAAM,OACxC,uCAAiE;AAEnE,WAAO,MAAM,wBAAwB;MACnC,UAAU,QAAQ;KACnB;EACH,GAAE;AAEF,QAAM,gBAAgB,QAAQ,SAAS,UAAU,QAAQ,gBAAgB;AAGzE,QAAM,iBAAiB,OAAO,YAAW;AAEvC,QAAI,CAAC,GAAG,cAAc,GAAG,eAAe,OAAO,MAAM,EAAE,MAAM,GAAE,CAAE,GAAG;AAClE,aAAO;QACL,UAAU;QACV,OAAO,CAAA;QACP,wBAAwB;QACxB,eAAe;;IAEnB;AAEA,UAAM,EAAE,sBAAqB,IAAK,MAAM,OACtC,yCAAgC;AAIlC,UAAM,WAAW,MAAM,oBAAoB;MACzC,UAAU,QAAQ;KACnB;AACD,UAAM,aAAqC,SAAS,UAAU,CAAA;AAC9D,UAAM,cAAc,WAAW,GAAG,UAAU;AAE5C,QAAI,CAAC,aAAa;AAChB,aAAO;QACL,UAAU;QACV,OAAO,CAAA;QACP,wBAAwB;QACxB,eAAe;;IAEnB;AAEA,UAAM,iBAAiB,MAAM,sBAAsB;MACjD,UAAU,QAAQ;MAClB,SAAS,QAAQ,QAAQ,QAAQ;;MACjC,eAAe;MACf;KACD;AAED,QAAI,CAAC,gBAAgB;AACnB,aAAO;QACL,UAAU;QACV,OAAO,CAAA;QACP,wBAAwB;QACxB,eAAe;;IAEnB;AAEA,WAAO;EACT,GAAE;AAIF,QAAM,WACJ,eAAe,YAAY,eAAe,aAAa,eACnD,eAAe,WACf,GAAG;AACT,QAAM,gBACJ,eAAe,kBAAkB,UACjC,eAAe,kBAAkB,aAC7B,eAAe,gBACf,GAAG;AAET,QAAM,aACH,gBAAgB,QAAQ,WAAY,OAAO,MAAM,aAAa;AACjE,QAAM,QAAQ,qBAAqB,QAAQ,IAAI,aAAa;AAC5D,QAAM,aACJ,CAAC,qBAAqB,QAAQ,KAAK,gBAAgB,KAC/C;IACE,WAAW;IACX,cAAc;MAEhB;AAEN,SAAO;IACL,UAAU,QAAQ;IAClB,SAAS,QAAQ,SAAS,YAAY,QAAQ,UAAU;IACxD,UAAU,QAAQ;IAClB;IACA;IACA;IACA,MAAM;IACN,WAAW;MACT;MACA;;;AAGN;", "names": ["getActiveClaimCondition"]}