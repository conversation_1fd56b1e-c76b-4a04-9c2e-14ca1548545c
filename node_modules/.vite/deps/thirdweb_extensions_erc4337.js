import {
  addSession<PERSON><PERSON>,
  defaultPermissions<PERSON>or<PERSON>d<PERSON>,
  getPermissionsFor<PERSON>igner,
  getUserOpHash,
  isAddSessionKeySupported,
  isSetPermissionsForSignerSupported,
  setPermissionsForSigner,
  shouldUpdateSessionKey,
  signPermissionRequest,
  toContractPermissions,
  userOperationRevertReasonEvent
} from "./chunk-26HKIFVU.js";
import "./chunk-EP7Z72A6.js";
import {
  once
} from "./chunk-NACC2RRT.js";
import "./chunk-JMHMJ42H.js";
import {
  isContractDeployed
} from "./chunk-GLPFE2IH.js";
import "./chunk-DKMNAJQI.js";
import {
  prepareEvent
} from "./chunk-OVBFYP7T.js";
import "./chunk-YCZ3YGMG.js";
import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import {
  prepareContractCall
} from "./chunk-Q44ZNVHQ.js";
import {
  readContract
} from "./chunk-A2MKHEYJ.js";
import "./chunk-TVIYKLDL.js";
import "./chunk-JOYHE2MS.js";
import "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-QGXAPRFG.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccount/write/validateUserOp.js
var FN_SELECTOR = "0x3a871cdd";
var FN_INPUTS = [
  {
    type: "tuple",
    name: "userOp",
    components: [
      {
        type: "address",
        name: "sender"
      },
      {
        type: "uint256",
        name: "nonce"
      },
      {
        type: "bytes",
        name: "initCode"
      },
      {
        type: "bytes",
        name: "callData"
      },
      {
        type: "uint256",
        name: "callGasLimit"
      },
      {
        type: "uint256",
        name: "verificationGasLimit"
      },
      {
        type: "uint256",
        name: "preVerificationGas"
      },
      {
        type: "uint256",
        name: "maxFeePerGas"
      },
      {
        type: "uint256",
        name: "maxPriorityFeePerGas"
      },
      {
        type: "bytes",
        name: "paymasterAndData"
      },
      {
        type: "bytes",
        name: "signature"
      }
    ]
  },
  {
    type: "bytes32",
    name: "userOpHash"
  },
  {
    type: "uint256",
    name: "missingAccountFunds"
  }
];
var FN_OUTPUTS = [
  {
    type: "uint256",
    name: "validationData"
  }
];
function isValidateUserOpSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/account/addAdmin.js
function addAdmin(options) {
  const { contract, account, adminAddress } = options;
  return setPermissionsForSigner({
    contract,
    async asyncParams() {
      const { req, signature } = await signPermissionRequest({
        account,
        contract,
        req: await defaultPermissionsForAdmin({
          target: adminAddress,
          action: "add-admin"
        })
      });
      return { signature, req };
    }
  });
}
function isAddAdminSupported(availableSelectors) {
  return isSetPermissionsForSignerSupported(availableSelectors);
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/account/removeAdmin.js
function removeAdmin(options) {
  const { contract, account, adminAddress } = options;
  return setPermissionsForSigner({
    contract,
    async asyncParams() {
      const { req, signature } = await signPermissionRequest({
        account,
        contract,
        req: await defaultPermissionsForAdmin({
          target: adminAddress,
          action: "remove-admin"
        })
      });
      return {
        signature,
        req
      };
    }
  });
}
function isRemoveAdminSupported(availableSelectors) {
  return isSetPermissionsForSignerSupported(availableSelectors);
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/account/removeSessionKey.js
function removeSessionKey(options) {
  const { contract, account, sessionKeyAddress } = options;
  return setPermissionsForSigner({
    contract,
    async asyncParams() {
      const { req, signature } = await signPermissionRequest({
        account,
        contract,
        req: await toContractPermissions({
          target: sessionKeyAddress,
          permissions: {
            approvedTargets: [],
            nativeTokenLimitPerTransaction: 0,
            permissionStartTimestamp: /* @__PURE__ */ new Date(0),
            permissionEndTimestamp: /* @__PURE__ */ new Date(0)
          }
        })
      });
      return { signature, req };
    }
  });
}
function isRemoveSessionKeySupported(availableSelectors) {
  return isSetPermissionsForSignerSupported(availableSelectors);
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountFactory/read/getAddress.js
var FN_SELECTOR2 = "0x8878ed33";
var FN_INPUTS2 = [
  {
    type: "address",
    name: "adminSigner"
  },
  {
    type: "bytes",
    name: "data"
  }
];
var FN_OUTPUTS2 = [
  {
    type: "address"
  }
];
function isGetAddressSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR2, FN_INPUTS2, FN_OUTPUTS2]
  });
}
async function getAddress(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR2, FN_INPUTS2, FN_OUTPUTS2],
    params: [options.adminSigner, options.data]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/account/isAccountDeployed.js
async function isAccountDeployed(options) {
  const predictedAddress = await getAddress(options);
  return isContractDeployed(getContract({
    ...options.contract,
    address: predictedAddress
  }));
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountFactory/write/createAccount.js
var FN_SELECTOR3 = "0xd8fd8f44";
var FN_INPUTS3 = [
  {
    type: "address",
    name: "admin"
  },
  {
    type: "bytes",
    name: "_data"
  }
];
var FN_OUTPUTS3 = [
  {
    type: "address",
    name: "account"
  }
];
function isCreateAccountSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR3, FN_INPUTS3, FN_OUTPUTS3]
  });
}
function createAccount(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR3, FN_INPUTS3, FN_OUTPUTS3],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [resolvedOptions.admin, resolvedOptions.data];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountPermissions/read/getAllActiveSigners.js
var FN_SELECTOR4 = "0x8b52d723";
var FN_INPUTS4 = [];
var FN_OUTPUTS4 = [
  {
    type: "tuple[]",
    name: "signers",
    components: [
      {
        type: "address",
        name: "signer"
      },
      {
        type: "address[]",
        name: "approvedTargets"
      },
      {
        type: "uint256",
        name: "nativeTokenLimitPerTransaction"
      },
      {
        type: "uint128",
        name: "startTimestamp"
      },
      {
        type: "uint128",
        name: "endTimestamp"
      }
    ]
  }
];
function isGetAllActiveSignersSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR4, FN_INPUTS4, FN_OUTPUTS4]
  });
}
async function getAllActiveSigners(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR4, FN_INPUTS4, FN_OUTPUTS4],
    params: []
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountPermissions/read/getAllAdmins.js
var FN_SELECTOR5 = "0xe9523c97";
var FN_INPUTS5 = [];
var FN_OUTPUTS5 = [
  {
    type: "address[]",
    name: "admins"
  }
];
function isGetAllAdminsSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR5, FN_INPUTS5, FN_OUTPUTS5]
  });
}
async function getAllAdmins(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR5, FN_INPUTS5, FN_OUTPUTS5],
    params: []
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountPermissions/read/getAllSigners.js
var FN_SELECTOR6 = "0xd42f2f35";
var FN_INPUTS6 = [];
var FN_OUTPUTS6 = [
  {
    type: "tuple[]",
    name: "signers",
    components: [
      {
        type: "address",
        name: "signer"
      },
      {
        type: "address[]",
        name: "approvedTargets"
      },
      {
        type: "uint256",
        name: "nativeTokenLimitPerTransaction"
      },
      {
        type: "uint128",
        name: "startTimestamp"
      },
      {
        type: "uint128",
        name: "endTimestamp"
      }
    ]
  }
];
function isGetAllSignersSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR6, FN_INPUTS6, FN_OUTPUTS6]
  });
}
async function getAllSigners(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR6, FN_INPUTS6, FN_OUTPUTS6],
    params: []
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountPermissions/read/isActiveSigner.js
var FN_SELECTOR7 = "0x7dff5a79";
var FN_INPUTS7 = [
  {
    type: "address",
    name: "signer"
  }
];
var FN_OUTPUTS7 = [
  {
    type: "bool"
  }
];
function isIsActiveSignerSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR7, FN_INPUTS7, FN_OUTPUTS7]
  });
}
async function isActiveSigner(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR7, FN_INPUTS7, FN_OUTPUTS7],
    params: [options.signer]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountPermissions/read/isAdmin.js
var FN_SELECTOR8 = "0x24d7806c";
var FN_INPUTS8 = [
  {
    type: "address",
    name: "signer"
  }
];
var FN_OUTPUTS8 = [
  {
    type: "bool"
  }
];
function isIsAdminSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR8, FN_INPUTS8, FN_OUTPUTS8]
  });
}
async function isAdmin(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR8, FN_INPUTS8, FN_OUTPUTS8],
    params: [options.signer]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountPermissions/events/AdminUpdated.js
function adminUpdatedEvent(filters = {}) {
  return prepareEvent({
    signature: "event AdminUpdated(address indexed signer, bool isAdmin)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountPermissions/events/SignerPermissionsUpdated.js
function signerPermissionsUpdatedEvent(filters = {}) {
  return prepareEvent({
    signature: "event SignerPermissionsUpdated(address indexed authorizingSigner, address indexed targetSigner, (address signer, uint8 isAdmin, address[] approvedTargets, uint256 nativeTokenLimitPerTransaction, uint128 permissionStartTimestamp, uint128 permissionEndTimestamp, uint128 reqValidityStartTimestamp, uint128 reqValidityEndTimestamp, bytes32 uid) permissions)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountFactory/read/getAllAccounts.js
var FN_SELECTOR9 = "0x08e93d0a";
var FN_INPUTS9 = [];
var FN_OUTPUTS9 = [
  {
    type: "address[]"
  }
];
function isGetAllAccountsSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR9, FN_INPUTS9, FN_OUTPUTS9]
  });
}
async function getAllAccounts(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR9, FN_INPUTS9, FN_OUTPUTS9],
    params: []
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountFactory/read/getAccounts.js
var FN_SELECTOR10 = "0xe68a7c3b";
var FN_INPUTS10 = [
  {
    type: "uint256",
    name: "start"
  },
  {
    type: "uint256",
    name: "end"
  }
];
var FN_OUTPUTS10 = [
  {
    type: "address[]"
  }
];
function isGetAccountsSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR10, FN_INPUTS10, FN_OUTPUTS10]
  });
}
async function getAccounts(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR10, FN_INPUTS10, FN_OUTPUTS10],
    params: [options.start, options.end]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountFactory/read/totalAccounts.js
var FN_SELECTOR11 = "0x58451f97";
var FN_INPUTS11 = [];
var FN_OUTPUTS11 = [
  {
    type: "uint256"
  }
];
function isTotalAccountsSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR11, FN_INPUTS11, FN_OUTPUTS11]
  });
}
async function totalAccounts(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR11, FN_INPUTS11, FN_OUTPUTS11],
    params: []
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountFactory/read/isRegistered.js
var FN_SELECTOR12 = "0xc3c5a547";
var FN_INPUTS12 = [
  {
    type: "address",
    name: "account"
  }
];
var FN_OUTPUTS12 = [
  {
    type: "bool"
  }
];
async function isRegistered(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR12, FN_INPUTS12, FN_OUTPUTS12],
    params: [options.account]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IAccountFactory/read/getAccountsOfSigner.js
var FN_SELECTOR13 = "0x0e6254fd";
var FN_INPUTS13 = [
  {
    type: "address",
    name: "signer"
  }
];
var FN_OUTPUTS13 = [
  {
    type: "address[]",
    name: "accounts"
  }
];
function isGetAccountsOfSignerSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR13, FN_INPUTS13, FN_OUTPUTS13]
  });
}
async function getAccountsOfSigner(options) {
  return readContract({
    contract: options.contract,
    method: [FN_SELECTOR13, FN_INPUTS13, FN_OUTPUTS13],
    params: [options.signer]
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IEntryPoint/events/AccountDeployed.js
function accountDeployedEvent(filters = {}) {
  return prepareEvent({
    signature: "event AccountDeployed(bytes32 indexed userOpHash, address indexed sender, address factory, address paymaster)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IEntryPoint/events/UserOperationEvent.js
function userOperationEventEvent(filters = {}) {
  return prepareEvent({
    signature: "event UserOperationEvent(bytes32 indexed userOpHash, address indexed sender, address indexed paymaster, uint256 nonce, bool success, uint256 actualGasCost, uint256 actualGasUsed)",
    filters
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc4337/__generated__/IEntryPoint/write/simulateHandleOp.js
var FN_SELECTOR14 = "0xd6383f94";
var FN_INPUTS14 = [
  {
    type: "tuple",
    name: "op",
    components: [
      {
        type: "address",
        name: "sender"
      },
      {
        type: "uint256",
        name: "nonce"
      },
      {
        type: "bytes",
        name: "initCode"
      },
      {
        type: "bytes",
        name: "callData"
      },
      {
        type: "uint256",
        name: "callGasLimit"
      },
      {
        type: "uint256",
        name: "verificationGasLimit"
      },
      {
        type: "uint256",
        name: "preVerificationGas"
      },
      {
        type: "uint256",
        name: "maxFeePerGas"
      },
      {
        type: "uint256",
        name: "maxPriorityFeePerGas"
      },
      {
        type: "bytes",
        name: "paymasterAndData"
      },
      {
        type: "bytes",
        name: "signature"
      }
    ]
  },
  {
    type: "address",
    name: "target"
  },
  {
    type: "bytes",
    name: "targetCallData"
  }
];
var FN_OUTPUTS14 = [];
function simulateHandleOp(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR14, FN_INPUTS14, FN_OUTPUTS14],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [
        resolvedOptions.op,
        resolvedOptions.target,
        resolvedOptions.targetCallData
      ];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}
export {
  accountDeployedEvent,
  addAdmin,
  addSessionKey,
  adminUpdatedEvent,
  createAccount,
  getAccounts,
  getAccountsOfSigner,
  getAllAccounts,
  getAllActiveSigners,
  getAllAdmins,
  getAllSigners,
  getPermissionsForSigner,
  getUserOpHash,
  isAccountDeployed,
  isActiveSigner,
  isAddAdminSupported,
  isAddSessionKeySupported,
  isAdmin,
  isCreateAccountSupported,
  isGetAccountsOfSignerSupported,
  isGetAccountsSupported,
  isGetAllAccountsSupported,
  isGetAllActiveSignersSupported,
  isGetAllAdminsSupported,
  isGetAllSignersSupported,
  isGetAddressSupported as isIsAccountDeployedSupported,
  isIsActiveSignerSupported,
  isIsAdminSupported,
  isGetAddressSupported as isPredictAccountAddressSupported,
  isRegistered,
  isRemoveAdminSupported,
  isRemoveSessionKeySupported,
  isTotalAccountsSupported,
  isValidateUserOpSupported,
  getAddress as predictAccountAddress,
  removeAdmin,
  removeSessionKey,
  shouldUpdateSessionKey,
  signerPermissionsUpdatedEvent,
  simulateHandleOp,
  totalAccounts,
  userOperationEventEvent,
  userOperationRevertReasonEvent
};
//# sourceMappingURL=thirdweb_extensions_erc4337.js.map
