import {
  sendAndConfirmTransaction,
  sendBatchTransaction
} from "./chunk-4ZUWD5XA.js";
import {
  eth_getTransactionReceipt
} from "./chunk-WVOZ6OAW.js";
import "./chunk-QWTK625L.js";
import "./chunk-S7XME4VF.js";
import "./chunk-DD6352NZ.js";
import "./chunk-Z2RZ5EOT.js";
import "./chunk-7HLZBJNZ.js";
import "./chunk-DVJU3TKU.js";
import "./chunk-DPFXQP4L.js";
import "./chunk-JOYHE2MS.js";
import {
  getRpcClient
} from "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import {
  randomBytesHex
} from "./chunk-N3KXRWQX.js";
import "./chunk-GYZW2ZZ6.js";
import "./chunk-26FWGFQH.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-DESKQC7P.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import {
  LruMap
} from "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/wallets/in-app/core/eip5972/in-app-wallet-calls.js
var bundlesToTransactions = new LruMap(1e3);
async function inAppWalletSendCalls(args) {
  const { account, calls } = args;
  const hashes = [];
  const id = randomBytesHex(65);
  bundlesToTransactions.set(id, hashes);
  if (account.sendBatchTransaction) {
    const receipt = await sendBatchTransaction({
      account,
      transactions: calls
    });
    hashes.push(receipt.transactionHash);
    bundlesToTransactions.set(id, hashes);
  } else {
    for (const tx of calls) {
      const receipt = await sendAndConfirmTransaction({
        account,
        transaction: tx
      });
      hashes.push(receipt.transactionHash);
      bundlesToTransactions.set(id, hashes);
    }
  }
  return id;
}
async function inAppWalletGetCallsStatus(args) {
  const { wallet, client, id } = args;
  const chain = wallet.getChain();
  if (!chain) {
    throw new Error("Failed to get calls status, no active chain found");
  }
  const bundle = bundlesToTransactions.get(id);
  if (!bundle) {
    throw new Error("Failed to get calls status, unknown bundle id");
  }
  const request = getRpcClient({ client, chain });
  let status = "success";
  const receipts = await Promise.all(bundle.map((hash) => eth_getTransactionReceipt(request, { hash }).then((receipt) => ({
    logs: receipt.logs.map((l) => ({
      address: l.address,
      data: l.data,
      topics: l.topics
    })),
    status: receipt.status,
    blockHash: receipt.blockHash,
    blockNumber: receipt.blockNumber,
    gasUsed: receipt.gasUsed,
    transactionHash: receipt.transactionHash
  })).catch(() => {
    status = "pending";
    return null;
  })));
  return {
    status,
    statusCode: 200,
    atomic: false,
    chainId: chain.id,
    id,
    version: "2.0.0",
    receipts: receipts.filter((r) => r !== null)
  };
}
export {
  inAppWalletGetCallsStatus,
  inAppWalletSendCalls
};
//# sourceMappingURL=in-app-wallet-calls-L4CRSGQH.js.map
