{"version": 3, "sources": ["../../thirdweb/src/wallets/in-app/core/eip5972/in-app-wallet-calls.ts"], "sourcesContent": ["import type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { eth_getTransactionReceipt } from \"../../../../rpc/actions/eth_getTransactionReceipt.js\";\nimport { getRpcClient } from \"../../../../rpc/rpc.js\";\nimport { sendAndConfirmTransaction } from \"../../../../transaction/actions/send-and-confirm-transaction.js\";\nimport { sendBatchTransaction } from \"../../../../transaction/actions/send-batch-transaction.js\";\nimport { LruMap } from \"../../../../utils/caching/lru.js\";\nimport type { Hex } from \"../../../../utils/encoding/hex.js\";\nimport { randomBytesHex } from \"../../../../utils/random.js\";\nimport type { PreparedSendCall } from \"../../../eip5792/send-calls.js\";\nimport type {\n  GetCallsStatusResponse,\n  WalletCallReceipt,\n} from \"../../../eip5792/types.js\";\nimport type { Account, Wallet } from \"../../../interfaces/wallet.js\";\n\nconst bundlesToTransactions = new LruMap<Hex[]>(1000);\n\n/**\n * @internal\n */\nexport async function inAppWalletSendCalls(args: {\n  account: Account;\n  calls: PreparedSendCall[];\n}): Promise<string> {\n  const { account, calls } = args;\n\n  const hashes: Hex[] = [];\n  const id = randomBytesHex(65);\n  bundlesToTransactions.set(id, hashes);\n  if (account.sendBatchTransaction) {\n    const receipt = await sendBatchTransaction({\n      account,\n      transactions: calls,\n    });\n    hashes.push(receipt.transactionHash);\n    bundlesToTransactions.set(id, hashes);\n  } else {\n    for (const tx of calls) {\n      const receipt = await sendAndConfirmTransaction({\n        account,\n        transaction: tx,\n      });\n      hashes.push(receipt.transactionHash);\n      bundlesToTransactions.set(id, hashes);\n    }\n  }\n\n  return id;\n}\n\n/**\n * @internal\n */\nexport async function inAppWalletGetCallsStatus(args: {\n  wallet: Wallet;\n  client: ThirdwebClient;\n  id: string;\n}): Promise<GetCallsStatusResponse> {\n  const { wallet, client, id } = args;\n\n  const chain = wallet.getChain();\n  if (!chain) {\n    throw new Error(\"Failed to get calls status, no active chain found\");\n  }\n\n  const bundle = bundlesToTransactions.get(id);\n  if (!bundle) {\n    throw new Error(\"Failed to get calls status, unknown bundle id\");\n  }\n\n  const request = getRpcClient({ client, chain });\n  let status: \"pending\" | \"success\" | \"failure\" = \"success\";\n  const receipts: (WalletCallReceipt<bigint, \"success\" | \"reverted\"> | null)[] =\n    await Promise.all(\n      bundle.map((hash) =>\n        eth_getTransactionReceipt(request, { hash })\n          .then((receipt) => ({\n            logs: receipt.logs.map((l) => ({\n              address: l.address,\n              data: l.data,\n              topics: l.topics,\n            })),\n            status: receipt.status,\n            blockHash: receipt.blockHash,\n            blockNumber: receipt.blockNumber,\n            gasUsed: receipt.gasUsed,\n            transactionHash: receipt.transactionHash,\n          }))\n          .catch(() => {\n            status = \"pending\";\n            return null; // Return null if there's an error to filter out later\n          }),\n      ),\n    );\n\n  return {\n    status,\n    statusCode: 200,\n    atomic: false,\n    chainId: chain.id,\n    id,\n    version: \"2.0.0\",\n    receipts: receipts.filter((r) => r !== null),\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,wBAAwB,IAAI,OAAc,GAAI;AAKpD,eAAsB,qBAAqB,MAG1C;AACC,QAAM,EAAE,SAAS,MAAK,IAAK;AAE3B,QAAM,SAAgB,CAAA;AACtB,QAAM,KAAK,eAAe,EAAE;AAC5B,wBAAsB,IAAI,IAAI,MAAM;AACpC,MAAI,QAAQ,sBAAsB;AAChC,UAAM,UAAU,MAAM,qBAAqB;MACzC;MACA,cAAc;KACf;AACD,WAAO,KAAK,QAAQ,eAAe;AACnC,0BAAsB,IAAI,IAAI,MAAM;EACtC,OAAO;AACL,eAAW,MAAM,OAAO;AACtB,YAAM,UAAU,MAAM,0BAA0B;QAC9C;QACA,aAAa;OACd;AACD,aAAO,KAAK,QAAQ,eAAe;AACnC,4BAAsB,IAAI,IAAI,MAAM;IACtC;EACF;AAEA,SAAO;AACT;AAKA,eAAsB,0BAA0B,MAI/C;AACC,QAAM,EAAE,QAAQ,QAAQ,GAAE,IAAK;AAE/B,QAAM,QAAQ,OAAO,SAAQ;AAC7B,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,mDAAmD;EACrE;AAEA,QAAM,SAAS,sBAAsB,IAAI,EAAE;AAC3C,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,+CAA+C;EACjE;AAEA,QAAM,UAAU,aAAa,EAAE,QAAQ,MAAK,CAAE;AAC9C,MAAI,SAA4C;AAChD,QAAM,WACJ,MAAM,QAAQ,IACZ,OAAO,IAAI,CAAC,SACV,0BAA0B,SAAS,EAAE,KAAI,CAAE,EACxC,KAAK,CAAC,aAAa;IAClB,MAAM,QAAQ,KAAK,IAAI,CAAC,OAAO;MAC7B,SAAS,EAAE;MACX,MAAM,EAAE;MACR,QAAQ,EAAE;MACV;IACF,QAAQ,QAAQ;IAChB,WAAW,QAAQ;IACnB,aAAa,QAAQ;IACrB,SAAS,QAAQ;IACjB,iBAAiB,QAAQ;IACzB,EACD,MAAM,MAAK;AACV,aAAS;AACT,WAAO;EACT,CAAC,CAAC,CACL;AAGL,SAAO;IACL;IACA,YAAY;IACZ,QAAQ;IACR,SAAS,MAAM;IACf;IACA,SAAS;IACT,UAAU,SAAS,OAAO,CAAC,MAAM,MAAM,IAAI;;AAE/C;", "names": []}