{"version": 3, "sources": ["../../thirdweb/src/extensions/erc20/__generated__/IERC20/read/totalSupply.ts", "../../thirdweb/src/extensions/erc20/__generated__/IERC20/write/transferFrom.ts", "../../thirdweb/src/extensions/erc20/read/isERC20.ts", "../../thirdweb/src/extensions/erc20/__generated__/IVotes/read/delegates.ts", "../../thirdweb/src/extensions/erc20/__generated__/IMintableERC20/write/mintTo.ts", "../../thirdweb/src/extensions/erc20/write/mintTo.ts", "../../thirdweb/src/extensions/erc20/write/transferFrom.ts", "../../thirdweb/src/extensions/common/__generated__/IMulticall/write/multicall.ts", "../../thirdweb/src/extensions/erc20/write/transferBatch.ts", "../../thirdweb/src/extensions/erc20/__generated__/IBurnableERC20/write/burn.ts", "../../thirdweb/src/extensions/erc20/__generated__/IBurnableERC20/write/burnFrom.ts", "../../thirdweb/src/extensions/erc20/__generated__/IDropERC20/read/claimCondition.ts", "../../thirdweb/src/extensions/erc20/drops/read/getClaimConditions.ts", "../../thirdweb/src/extensions/erc20/__generated__/DropERC20/read/verifyClaim.ts", "../../thirdweb/src/extensions/erc20/drops/read/canClaim.ts", "../../thirdweb/src/utils/extensions/drops/process-override-list.ts", "../../thirdweb/src/utils/extensions/drops/get-multicall-set-claim-claim-conditon-transactions.ts", "../../thirdweb/src/extensions/erc20/__generated__/IDropERC20/write/setClaimConditions.ts", "../../thirdweb/src/extensions/erc20/drops/write/setClaimConditions.ts", "../../thirdweb/src/extensions/erc20/drops/write/resetClaimEligibility.ts", "../../thirdweb/src/extensions/erc20/__generated__/ISignatureMintERC20/write/mintWithSignature.ts", "../../thirdweb/src/extensions/erc20/write/sigMint.ts", "../../thirdweb/src/extensions/erc20/__generated__/IWETH/write/deposit.ts", "../../thirdweb/src/extensions/erc20/write/deposit.ts", "../../thirdweb/src/extensions/erc20/__generated__/IWETH/write/withdraw.ts", "../../thirdweb/src/extensions/erc20/__generated__/IERC20/events/Transfer.ts", "../../thirdweb/src/extensions/erc20/__generated__/IERC20/events/Approval.ts", "../../thirdweb/src/extensions/erc20/__generated__/IMintableERC20/events/TokensMinted.ts", "../../thirdweb/src/extensions/erc20/__generated__/IDropERC20/events/TokensClaimed.ts", "../../thirdweb/src/extensions/erc20/__generated__/IDropERC20/events/ClaimConditionsUpdated.ts", "../../thirdweb/src/extensions/erc20/__generated__/ISignatureMintERC20/events/TokensMintedWithSignature.ts", "../../thirdweb/src/extensions/erc20/__generated__/IVotes/write/delegate.ts"], "sourcesContent": ["import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0x18160ddd\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n  },\n] as const;\n\n/**\n * Checks if the `totalSupply` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `totalSupply` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isTotalSupplySupported } from \"thirdweb/extensions/erc20\";\n * const supported = isTotalSupplySupported([\"0x...\"]);\n * ```\n */\nexport function isTotalSupplySupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the totalSupply function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC20\n * @example\n * ```ts\n * import { decodeTotalSupplyResult } from \"thirdweb/extensions/erc20\";\n * const result = decodeTotalSupplyResultResult(\"...\");\n * ```\n */\nexport function decodeTotalSupplyResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"totalSupply\" function on the contract.\n * @param options - The options for the totalSupply function.\n * @returns The parsed result of the function call.\n * @extension ERC20\n * @example\n * ```ts\n * import { totalSupply } from \"thirdweb/extensions/erc20\";\n *\n * const result = await totalSupply({\n *  contract,\n * });\n *\n * ```\n */\nexport async function totalSupply(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"transferFrom\" function.\n */\nexport type TransferFromParams = WithOverrides<{\n  from: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"from\" }>;\n  to: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"to\" }>;\n  value: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"value\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x23b872dd\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"from\",\n  },\n  {\n    type: \"address\",\n    name: \"to\",\n  },\n  {\n    type: \"uint256\",\n    name: \"value\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bool\",\n  },\n] as const;\n\n/**\n * Checks if the `transferFrom` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `transferFrom` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isTransferFromSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isTransferFromSupported([\"0x...\"]);\n * ```\n */\nexport function isTransferFromSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"transferFrom\" function.\n * @param options - The options for the transferFrom function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeTransferFromParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeTransferFromParams({\n *  from: ...,\n *  to: ...,\n *  value: ...,\n * });\n * ```\n */\nexport function encodeTransferFromParams(options: TransferFromParams) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.from,\n    options.to,\n    options.value,\n  ]);\n}\n\n/**\n * Encodes the \"transferFrom\" function into a Hex string with its parameters.\n * @param options - The options for the transferFrom function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeTransferFrom } from \"thirdweb/extensions/erc20\";\n * const result = encodeTransferFrom({\n *  from: ...,\n *  to: ...,\n *  value: ...,\n * });\n * ```\n */\nexport function encodeTransferFrom(options: TransferFromParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeTransferFromParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"transferFrom\" function on the contract.\n * @param options - The options for the \"transferFrom\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { transferFrom } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = transferFrom({\n *  contract,\n *  from: ...,\n *  to: ...,\n *  value: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function transferFrom(\n  options: BaseTransactionOptions<\n    | TransferFromParams\n    | {\n        asyncParams: () => Promise<TransferFromParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.from,\n        resolvedOptions.to,\n        resolvedOptions.value,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import { isNameSupported } from \"../../common/__generated__/IContractMetadata/read/name.js\";\nimport { isSymbolSupported } from \"../../common/__generated__/IContractMetadata/read/symbol.js\";\nimport { isAllowanceSupported } from \"../__generated__/IERC20/read/allowance.js\";\nimport { isBalanceOfSupported } from \"../__generated__/IERC20/read/balanceOf.js\";\nimport { isDecimalsSupported } from \"../__generated__/IERC20/read/decimals.js\";\nimport { isTotalSupplySupported } from \"../__generated__/IERC20/read/totalSupply.js\";\nimport { isApproveSupported } from \"../__generated__/IERC20/write/approve.js\";\nimport { isTransferSupported } from \"../__generated__/IERC20/write/transfer.js\";\nimport { isTransferFromSupported } from \"../__generated__/IERC20/write/transferFrom.js\";\n\n/**\n * Check if a contract is an ERC20 token.\n * @param options - The transaction options.\n * @returns A boolean indicating whether the contract is an ERC20 token.\n * @extension ERC20\n * @example\n * ```ts\n * import { isERC20 } from \"thirdweb/extensions/erc20\";\n * import { resolveContractAbi } from \"thirdweb/contract\";\n *\n * const abi = await resolveContractAbi(contract);\n * const selectors = abi\n *   .filter((f) => f.type === \"function\")\n *   .map((f) => toFunctionSelector(f));\n *\n * const result = await isERC20(selectors);\n * ```\n */\nexport function isERC20(availableSelectors: string[]) {\n  // there is no trustworthy way to check if a contract is ERC20 via ERC165, so we do this manually.\n  // see: https://github.com/OpenZeppelin/openzeppelin-contracts/issues/3575\n  // see: https://ethereum.org/en/developers/docs/standards/tokens/erc-20/\n\n  return [\n    isNameSupported(availableSelectors),\n    isSymbolSupported(availableSelectors),\n    isDecimalsSupported(availableSelectors),\n    isTotalSupplySupported(availableSelectors),\n    isBalanceOfSupported(availableSelectors),\n    isTransferSupported(availableSelectors),\n    isTransferFromSupported(availableSelectors),\n    isApproveSupported(availableSelectors),\n    isAllowanceSupported(availableSelectors),\n  ].every(Boolean);\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"delegates\" function.\n */\nexport type DelegatesParams = {\n  account: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"account\" }>;\n};\n\nexport const FN_SELECTOR = \"0x587cde1e\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"account\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address\",\n  },\n] as const;\n\n/**\n * Checks if the `delegates` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `delegates` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isDelegatesSupported } from \"thirdweb/extensions/erc20\";\n * const supported = isDelegatesSupported([\"0x...\"]);\n * ```\n */\nexport function isDelegatesSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"delegates\" function.\n * @param options - The options for the delegates function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeDelegatesParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeDelegatesParams({\n *  account: ...,\n * });\n * ```\n */\nexport function encodeDelegatesParams(options: DelegatesParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.account]);\n}\n\n/**\n * Encodes the \"delegates\" function into a Hex string with its parameters.\n * @param options - The options for the delegates function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeDelegates } from \"thirdweb/extensions/erc20\";\n * const result = encodeDelegates({\n *  account: ...,\n * });\n * ```\n */\nexport function encodeDelegates(options: DelegatesParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeDelegatesParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the delegates function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC20\n * @example\n * ```ts\n * import { decodeDelegatesResult } from \"thirdweb/extensions/erc20\";\n * const result = decodeDelegatesResultResult(\"...\");\n * ```\n */\nexport function decodeDelegatesResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"delegates\" function on the contract.\n * @param options - The options for the delegates function.\n * @returns The parsed result of the function call.\n * @extension ERC20\n * @example\n * ```ts\n * import { delegates } from \"thirdweb/extensions/erc20\";\n *\n * const result = await delegates({\n *  contract,\n *  account: ...,\n * });\n *\n * ```\n */\nexport async function delegates(\n  options: BaseTransactionOptions<DelegatesParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.account],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"mintTo\" function.\n */\nexport type MintToParams = WithOverrides<{\n  to: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"to\" }>;\n  amount: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"amount\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x449a52f8\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"to\",\n  },\n  {\n    type: \"uint256\",\n    name: \"amount\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `mintTo` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `mintTo` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isMintToSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isMintToSupported([\"0x...\"]);\n * ```\n */\nexport function isMintToSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"mintTo\" function.\n * @param options - The options for the mintTo function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeMintToParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeMintToParams({\n *  to: ...,\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeMintToParams(options: MintToParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.to, options.amount]);\n}\n\n/**\n * Encodes the \"mintTo\" function into a Hex string with its parameters.\n * @param options - The options for the mintTo function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeMintTo } from \"thirdweb/extensions/erc20\";\n * const result = encodeMintTo({\n *  to: ...,\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeMintTo(options: MintToParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeMintToParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"mintTo\" function on the contract.\n * @param options - The options for the \"mintTo\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { mintTo } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = mintTo({\n *  contract,\n *  to: ...,\n *  amount: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function mintTo(\n  options: BaseTransactionOptions<\n    | MintToParams\n    | {\n        asyncParams: () => Promise<MintToParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.to, resolvedOptions.amount] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../transaction/types.js\";\nimport type { Prettify } from \"../../../utils/type-utils.js\";\nimport { toUnits } from \"../../../utils/units.js\";\nimport { mintTo as generatedMintTo } from \"../__generated__/IMintableERC20/write/mintTo.js\";\n\nexport { isMintToSupported } from \"../__generated__/IMintableERC20/write/mintTo.js\";\n\n/**\n * Represents the parameters for the `mintTo` function.\n * @extension ERC20\n */\nexport type MintToParams = Prettify<\n  WithOverrides<\n    { to: string } & (\n      | {\n          amount: number | string;\n        }\n      | {\n          amountWei: bigint;\n        }\n    )\n  >\n>;\n\n/**\n * Mints a specified amount of tokens to a given address.\n * This method is only available on the `TokenERC20` contract.\n * @param options - The options for minting tokens.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { mintTo } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = mintTo({\n *  contract,\n *  to: \"0x...\",\n *  amount: 100,\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function mintTo(options: BaseTransactionOptions<MintToParams>) {\n  return generatedMintTo({\n    contract: options.contract,\n    asyncParams: async () => {\n      let amount: bigint;\n      if (\"amount\" in options) {\n        // if we need to parse the amount from ether to gwei then we pull in the decimals extension\n        const { decimals } = await import(\"../read/decimals.js\");\n        // if this fails we fall back to `18` decimals\n        const d = await decimals(options).catch(() => 18);\n        // turn ether into gwei\n        amount = toUnits(options.amount.toString(), d);\n      } else {\n        amount = options.amountWei;\n      }\n      return {\n        to: options.to,\n        amount: amount,\n        overrides: options.overrides,\n      } as const;\n    },\n  });\n}\n", "import type { Address } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../transaction/types.js\";\nimport { toUnits } from \"../../../utils/units.js\";\n\nimport type { Prettify } from \"../../../utils/type-utils.js\";\nimport { transferFrom as generatedTransferFrom } from \"../__generated__/IERC20/write/transferFrom.js\";\n\n/**\n * Represents the parameters for the `transferFrom` function.\n * @extension ERC20\n */\nexport type TransferFromParams = Prettify<\n  WithOverrides<\n    { to: Address; from: Address } & (\n      | {\n          amount: number | string;\n        }\n      | {\n          amountWei: bigint;\n        }\n    )\n  >\n>;\n\n/**\n * Transfers a specified amount of tokens from one address to another address on the ERC20 contract.\n * @param options - The transaction options including from, to, amount, and gas price.\n * @returns A promise that resolves to the prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { transferFrom } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = transferFrom({\n *  contract: USDC_CONTRACT,\n *  from: \"0x1234...\",\n *  to: \"0x5678...\",\n *  amount: 100,\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function transferFrom(\n  options: BaseTransactionOptions<TransferFromParams>,\n) {\n  return generatedTransferFrom({\n    contract: options.contract,\n    asyncParams: async () => {\n      let amount: bigint;\n      if (\"amount\" in options) {\n        // if we need to parse the amount from ether to gwei then we pull in the decimals extension\n        const { decimals } = await import(\"../read/decimals.js\");\n        // if this fails we fall back to `18` decimals\n        const d = await decimals(options).catch(() => 18);\n        // turn ether into gwei\n        amount = toUnits(options.amount.toString(), d);\n      } else {\n        amount = options.amountWei;\n      }\n      return {\n        from: options.from,\n        to: options.to,\n        value: amount,\n        overrides: {\n          erc20Value: {\n            amountWei: amount,\n            tokenAddress: options.contract.address,\n          },\n          ...options.overrides,\n        },\n      } as const;\n    },\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"multicall\" function.\n */\nexport type MulticallParams = WithOverrides<{\n  data: AbiParameterToPrimitiveType<{ type: \"bytes[]\"; name: \"data\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0xac9650d8\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"bytes[]\",\n    name: \"data\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bytes[]\",\n    name: \"results\",\n  },\n] as const;\n\n/**\n * Checks if the `multicall` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `multicall` method is supported.\n * @extension COMMON\n * @example\n * ```ts\n * import { isMulticallSupported } from \"thirdweb/extensions/common\";\n *\n * const supported = isMulticallSupported([\"0x...\"]);\n * ```\n */\nexport function isMulticallSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"multicall\" function.\n * @param options - The options for the multicall function.\n * @returns The encoded ABI parameters.\n * @extension COMMON\n * @example\n * ```ts\n * import { encodeMulticallParams } from \"thirdweb/extensions/common\";\n * const result = encodeMulticallParams({\n *  data: ...,\n * });\n * ```\n */\nexport function encodeMulticallParams(options: MulticallParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.data]);\n}\n\n/**\n * Encodes the \"multicall\" function into a Hex string with its parameters.\n * @param options - The options for the multicall function.\n * @returns The encoded hexadecimal string.\n * @extension COMMON\n * @example\n * ```ts\n * import { encodeMulticall } from \"thirdweb/extensions/common\";\n * const result = encodeMulticall({\n *  data: ...,\n * });\n * ```\n */\nexport function encodeMulticall(options: MulticallParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeMulticallParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"multicall\" function on the contract.\n * @param options - The options for the \"multicall\" function.\n * @returns A prepared transaction object.\n * @extension COMMON\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { multicall } from \"thirdweb/extensions/common\";\n *\n * const transaction = multicall({\n *  contract,\n *  data: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function multicall(\n  options: BaseTransactionOptions<\n    | MulticallParams\n    | {\n        asyncParams: () => Promise<MulticallParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.data] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport type { Prettify } from \"../../../utils/type-utils.js\";\nimport { toUnits } from \"../../../utils/units.js\";\nimport { multicall } from \"../../common/__generated__/IMulticall/write/multicall.js\";\nimport { encodeTransfer } from \"../__generated__/IERC20/write/transfer.js\";\n\n/**\n * Represents the parameters for a batch transfer operation.\n * @extension ERC20\n */\nexport type TransferBatchParams = Prettify<{\n  batch: Array<\n    { to: string } & (\n      | {\n          amount: number | string;\n        }\n      | {\n          amountWei: bigint;\n        }\n    )\n  >;\n}>;\n\n/**\n * Transfers a batch of ERC20 tokens from the sender's address to the specified recipient address.\n * @param options - The options for the batch transfer transaction.\n * @returns A promise that resolves to the prepared transaction.\n * @extension ERC20\n * @example\n * ```ts\n * import { transferBatch } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = transferBatch({\n *  contract,\n *  batch: [\n *    {\n *      to: \"0x...\",\n *      amount: 100,\n *    },\n *    {\n *      to: \"0x...\",\n *      amount: \"0.1\",\n *    },\n * ]);\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function transferBatch(\n  options: BaseTransactionOptions<TransferBatchParams>,\n) {\n  return multicall({\n    contract: options.contract,\n    asyncParams: async () => {\n      const content = await optimizeTransferContent(options);\n      return {\n        data: content.map((item) => {\n          return encodeTransfer({\n            to: item.to,\n            value: item.amountWei,\n            overrides: {\n              erc20Value: {\n                amountWei: item.amountWei,\n                tokenAddress: options.contract.address,\n              },\n            },\n          });\n        }),\n      };\n    },\n  });\n}\n\n/**\n * Records with the same recipient (`to`) can be packed into one transaction\n * For example, the data below:\n * ```ts\n * [\n *   {\n *     to: \"wallet-a\",\n *     amount: 1,\n *   },\n *   {\n *     to: \"wallet-A\",\n *     amountWei: 1000000000000000000n,\n *   },\n * ]\n * ```\n *\n * can be packed to:\n * ```ts\n * [\n *   {\n *     to: \"wallet-a\",\n *     amountWei: 2000000000000000000n,\n *   },\n * ]\n * ```\n * @internal\n */\nexport async function optimizeTransferContent(\n  options: BaseTransactionOptions<TransferBatchParams>,\n): Promise<Array<{ to: string; amountWei: bigint }>> {\n  const groupedRecords = await options.batch.reduce(\n    async (accPromise, record) => {\n      const acc = await accPromise;\n      let amountInWei: bigint;\n      if (\"amount\" in record) {\n        // it's OK to call this multiple times because the call is cached\n        const { decimals } = await import(\"../read/decimals.js\");\n        // if this fails we fall back to `18` decimals\n        const d = await decimals(options).catch(() => undefined);\n        if (d === undefined) {\n          throw new Error(\n            `Failed to get the decimals for contract: ${options.contract.address}`,\n          );\n        }\n        amountInWei = toUnits(record.amount.toString(), d);\n      } else {\n        amountInWei = record.amountWei;\n      }\n      const existingRecord = acc.find(\n        (r) => r.to.toLowerCase() === record.to.toLowerCase(),\n      );\n      if (existingRecord) {\n        existingRecord.amountWei = existingRecord.amountWei + amountInWei;\n      } else {\n        acc.push({\n          to: record.to,\n          amountWei: amountInWei,\n        });\n      }\n\n      return acc;\n    },\n    Promise.resolve([] as { to: string; amountWei: bigint }[]),\n  );\n  return groupedRecords;\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"burn\" function.\n */\nexport type BurnParams = WithOverrides<{\n  amount: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"amount\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x42966c68\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"amount\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `burn` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `burn` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isBurnSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isBurnSupported([\"0x...\"]);\n * ```\n */\nexport function isBurnSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"burn\" function.\n * @param options - The options for the burn function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeBurnParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeBurnParams({\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeBurnParams(options: BurnParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.amount]);\n}\n\n/**\n * Encodes the \"burn\" function into a Hex string with its parameters.\n * @param options - The options for the burn function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeBurn } from \"thirdweb/extensions/erc20\";\n * const result = encodeBurn({\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeBurn(options: BurnParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeBurnParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"burn\" function on the contract.\n * @param options - The options for the \"burn\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { burn } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = burn({\n *  contract,\n *  amount: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function burn(\n  options: BaseTransactionOptions<\n    | BurnParams\n    | {\n        asyncParams: () => Promise<BurnParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.amount] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"burnFrom\" function.\n */\nexport type BurnFromParams = WithOverrides<{\n  account: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"account\" }>;\n  amount: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"amount\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x79cc6790\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"account\",\n  },\n  {\n    type: \"uint256\",\n    name: \"amount\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `burnFrom` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `burnFrom` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isBurnFromSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isBurnFromSupported([\"0x...\"]);\n * ```\n */\nexport function isBurnFromSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"burnFrom\" function.\n * @param options - The options for the burnFrom function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeBurnFromParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeBurnFromParams({\n *  account: ...,\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeBurnFromParams(options: BurnFromParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.account, options.amount]);\n}\n\n/**\n * Encodes the \"burnFrom\" function into a Hex string with its parameters.\n * @param options - The options for the burnFrom function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeBurnFrom } from \"thirdweb/extensions/erc20\";\n * const result = encodeBurnFrom({\n *  account: ...,\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeBurnFrom(options: BurnFromParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeBurnFromParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"burnFrom\" function on the contract.\n * @param options - The options for the \"burnFrom\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { burnFrom } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = burnFrom({\n *  contract,\n *  account: ...,\n *  amount: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function burnFrom(\n  options: BaseTransactionOptions<\n    | BurnFromParams\n    | {\n        asyncParams: () => Promise<BurnFromParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.account, resolvedOptions.amount] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\n\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0xd637ed59\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n    name: \"currentStartId\",\n  },\n  {\n    type: \"uint256\",\n    name: \"count\",\n  },\n] as const;\n\n/**\n * Checks if the `claimCondition` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `claimCondition` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isClaimConditionSupported } from \"thirdweb/extensions/erc20\";\n * const supported = isClaimConditionSupported([\"0x...\"]);\n * ```\n */\nexport function isClaimConditionSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Decodes the result of the claimCondition function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC20\n * @example\n * ```ts\n * import { decodeClaimConditionResult } from \"thirdweb/extensions/erc20\";\n * const result = decodeClaimConditionResultResult(\"...\");\n * ```\n */\nexport function decodeClaimConditionResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result);\n}\n\n/**\n * Calls the \"claimCondition\" function on the contract.\n * @param options - The options for the claimCondition function.\n * @returns The parsed result of the function call.\n * @extension ERC20\n * @example\n * ```ts\n * import { claimCondition } from \"thirdweb/extensions/erc20\";\n *\n * const result = await claimCondition({\n *  contract,\n * });\n *\n * ```\n */\nexport async function claimCondition(options: BaseTransactionOptions) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [],\n  });\n}\n", "import type { BaseTransactionOptions } from \"../../../../transaction/types.js\";\nimport type { ClaimCondition } from \"../../../../utils/extensions/drops/types.js\";\nimport * as MultiPhase from \"../../__generated__/IDropERC20/read/claimCondition.js\";\nimport * as MultiById from \"../../__generated__/IDropERC20/read/getClaimConditionById.js\";\n\n/**\n * Retrieves all claim conditions.\n * This method is only available on the `DropERC20` contract.\n * @param options - The transaction options.\n * @returns A promise that resolves to all claim conditions.\n * @throws An error if the claim conditions are unsupported by the contract.\n * @extension ERC20\n * @example\n * ```ts\n * import { getClaimConditions } from \"thirdweb/extensions/erc20\";\n * const conditions = await getClaimConditions({ contract });\n * ```\n */\nexport async function getClaimConditions(\n  options: BaseTransactionOptions,\n): Promise<ClaimCondition[]> {\n  try {\n    const [startId, count] = await MultiPhase.claimCondition(options);\n\n    const conditionPromises: Array<\n      ReturnType<typeof MultiById.getClaimConditionById>\n    > = [];\n    for (let i = startId; i < startId + count; i++) {\n      conditionPromises.push(\n        MultiById.getClaimConditionById({\n          ...options,\n          conditionId: i,\n        }),\n      );\n    }\n    return Promise.all(conditionPromises);\n  } catch {\n    throw new Error(\"Claim condition not found\");\n  }\n}\n\n/**\n * Checks if the `getClaimConditions` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getClaimConditions` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isGetClaimConditionsSupported } from \"thirdweb/extensions/erc721\";\n *\n * const supported = isGetClaimConditionsSupported([\"0x...\"]);\n * ```\n */\nexport function isGetClaimConditionsSupported(availableSelectors: string[]) {\n  // if multi phase is supported, return true\n  return (\n    MultiPhase.isClaimConditionSupported(availableSelectors) &&\n    MultiById.isGetClaimConditionByIdSupported(availableSelectors)\n  );\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"verifyClaim\" function.\n */\nexport type VerifyClaimParams = {\n  conditionId: AbiParameterToPrimitiveType<{\n    type: \"uint256\";\n    name: \"_conditionId\";\n  }>;\n  claimer: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"_claimer\" }>;\n  quantity: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"_quantity\" }>;\n  currency: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"_currency\" }>;\n  pricePerToken: AbiParameterToPrimitiveType<{\n    type: \"uint256\";\n    name: \"_pricePerToken\";\n  }>;\n  allowlistProof: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"_allowlistProof\";\n    components: [\n      { type: \"bytes32[]\"; name: \"proof\" },\n      { type: \"uint256\"; name: \"quantityLimitPerWallet\" },\n      { type: \"uint256\"; name: \"pricePerToken\" },\n      { type: \"address\"; name: \"currency\" },\n    ];\n  }>;\n};\n\nexport const FN_SELECTOR = \"0x23a2902b\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"_conditionId\",\n  },\n  {\n    type: \"address\",\n    name: \"_claimer\",\n  },\n  {\n    type: \"uint256\",\n    name: \"_quantity\",\n  },\n  {\n    type: \"address\",\n    name: \"_currency\",\n  },\n  {\n    type: \"uint256\",\n    name: \"_pricePerToken\",\n  },\n  {\n    type: \"tuple\",\n    name: \"_allowlistProof\",\n    components: [\n      {\n        type: \"bytes32[]\",\n        name: \"proof\",\n      },\n      {\n        type: \"uint256\",\n        name: \"quantityLimitPerWallet\",\n      },\n      {\n        type: \"uint256\",\n        name: \"pricePerToken\",\n      },\n      {\n        type: \"address\",\n        name: \"currency\",\n      },\n    ],\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bool\",\n    name: \"isOverride\",\n  },\n] as const;\n\n/**\n * Checks if the `verifyClaim` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `verifyClaim` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isVerifyClaimSupported } from \"thirdweb/extensions/erc20\";\n * const supported = isVerifyClaimSupported([\"0x...\"]);\n * ```\n */\nexport function isVerifyClaimSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"verifyClaim\" function.\n * @param options - The options for the verifyClaim function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeVerifyClaimParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeVerifyClaimParams({\n *  conditionId: ...,\n *  claimer: ...,\n *  quantity: ...,\n *  currency: ...,\n *  pricePerToken: ...,\n *  allowlistProof: ...,\n * });\n * ```\n */\nexport function encodeVerifyClaimParams(options: VerifyClaimParams) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.conditionId,\n    options.claimer,\n    options.quantity,\n    options.currency,\n    options.pricePerToken,\n    options.allowlistProof,\n  ]);\n}\n\n/**\n * Encodes the \"verifyClaim\" function into a Hex string with its parameters.\n * @param options - The options for the verifyClaim function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeVerifyClaim } from \"thirdweb/extensions/erc20\";\n * const result = encodeVerifyClaim({\n *  conditionId: ...,\n *  claimer: ...,\n *  quantity: ...,\n *  currency: ...,\n *  pricePerToken: ...,\n *  allowlistProof: ...,\n * });\n * ```\n */\nexport function encodeVerifyClaim(options: VerifyClaimParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeVerifyClaimParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the verifyClaim function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC20\n * @example\n * ```ts\n * import { decodeVerifyClaimResult } from \"thirdweb/extensions/erc20\";\n * const result = decodeVerifyClaimResultResult(\"...\");\n * ```\n */\nexport function decodeVerifyClaimResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"verifyClaim\" function on the contract.\n * @param options - The options for the verifyClaim function.\n * @returns The parsed result of the function call.\n * @extension ERC20\n * @example\n * ```ts\n * import { verifyClaim } from \"thirdweb/extensions/erc20\";\n *\n * const result = await verifyClaim({\n *  contract,\n *  conditionId: ...,\n *  claimer: ...,\n *  quantity: ...,\n *  currency: ...,\n *  pricePerToken: ...,\n *  allowlistProof: ...,\n * });\n *\n * ```\n */\nexport async function verifyClaim(\n  options: BaseTransactionOptions<VerifyClaimParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [\n      options.conditionId,\n      options.claimer,\n      options.quantity,\n      options.currency,\n      options.pricePerToken,\n      options.allowlistProof,\n    ],\n  });\n}\n", "import { extractErrorResult } from \"../../../../transaction/extract-error.js\";\nimport type { BaseTransactionOptions } from \"../../../../transaction/types.js\";\nimport { getClaimParams } from \"../../../../utils/extensions/drops/get-claim-params.js\";\nimport { verifyClaim } from \"../../__generated__/DropERC20/read/verifyClaim.js\";\nimport { getActiveClaimConditionId } from \"../../__generated__/IDropERC20/read/getActiveClaimConditionId.js\";\nimport { decimals } from \"../../read/decimals.js\";\n\nexport type CanClaimParams = {\n  claimer: string;\n  from?: string;\n} & ({ quantityInWei: bigint } | { quantity: string });\n\nexport type CanClaimResult = {\n  result: boolean;\n  reason?: string;\n};\n\n/**\n * Check if a user can claim a drop.\n * This method is only available on the `DropERC20` contract.\n * @param options - The options for the transaction.\n * @returns Whether the user can claim the drop.\n *\n * @example\n * ```ts\n * const claimResult = await canClaim({\n *   contract: contract,\n *   claimer: \"******************************************\",\n *   quantity: \"1\",\n * });\n * ```\n *\n * @extension ERC20\n */\nexport async function canClaim(\n  options: BaseTransactionOptions<CanClaimParams>,\n): Promise<CanClaimResult> {\n  const quantityWei = await (async () => {\n    if (\"quantityInWei\" in options) {\n      return options.quantityInWei;\n    }\n\n    const { toUnits } = await import(\"../../../../utils/units.js\");\n    return toUnits(\n      options.quantity,\n      await decimals({ contract: options.contract }),\n    );\n  })();\n  const [conditionId, { quantity, currency, pricePerToken, allowlistProof }] =\n    await Promise.all([\n      getActiveClaimConditionId({\n        contract: options.contract,\n      }),\n      getClaimParams({\n        contract: options.contract,\n        quantity: quantityWei,\n        to: options.claimer,\n        type: \"erc20\",\n        from: options.from,\n        tokenDecimals: await decimals({ contract: options.contract }),\n      }),\n    ]);\n  try {\n    await verifyClaim({\n      contract: options.contract,\n      claimer: options.claimer,\n      quantity,\n      currency,\n      pricePerToken,\n      allowlistProof,\n      conditionId,\n    });\n    return {\n      result: true,\n    };\n  } catch (error) {\n    return {\n      result: false,\n      reason: await extractErrorResult({ error, contract: options.contract }),\n    };\n  }\n}\n", "import type { Chain } from \"../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport { MerkleTree } from \"../../../merkletree/MerkleTree.js\";\nimport { upload } from \"../../../storage/upload.js\";\nimport type { Hex } from \"../../encoding/hex.js\";\nimport { stringify } from \"../../json.js\";\nimport { hashEntry } from \"./hash-entry.js\";\nimport type {\n  OverrideEntry,\n  ShardData,\n  ShardedMerkleTreeInfo,\n} from \"./types.js\";\n\nexport async function processOverrideList(options: {\n  client: ThirdwebClient;\n  chain: Chain;\n  overrides: OverrideEntry[];\n  tokenDecimals: number;\n  shardNybbles?: number;\n  hashEntry?: (options: {\n    entry: OverrideEntry;\n    chain: Chain;\n    client: ThirdwebClient;\n    tokenDecimals: number;\n  }) => Promise<Hex>;\n}) {\n  const hashEntryFn = options.hashEntry || hashEntry;\n  const shardNybbles = options.shardNybbles || 2;\n  // 2. shard them into a map where the key is the first n digits of the address\n  const shards: Record<string, OverrideEntry[]> = {};\n  for (const snapshotEntry of options.overrides) {\n    const shard = snapshotEntry.address\n      .slice(2, 2 + shardNybbles)\n      .toLowerCase();\n    if (shards[shard] === undefined) {\n      shards[shard] = [];\n    }\n    // biome-ignore lint/style/noNonNullAssertion: we know it's defined\n    shards[shard]!.push(snapshotEntry);\n  }\n  // 3. create the merkle subtrees for each shard\n  const subTrees = await Promise.all(\n    Object.entries(shards).map(async ([shard, entries]) => [\n      shard,\n      new MerkleTree(\n        await Promise.all(\n          entries.map(async (entry) => {\n            return hashEntryFn({\n              entry,\n              chain: options.chain,\n              client: options.client,\n              tokenDecimals: options.tokenDecimals,\n            });\n          }),\n        ),\n      ).getHexRoot(),\n    ]),\n  );\n  // 4. create the master merkle tree from all the subtrees\n  const roots: Record<string, Hex> = Object.fromEntries(subTrees);\n  const tree = new MerkleTree(Object.values(roots));\n  // 5. upload all the shards with filename <shardId>.json to easily retrieve\n  const shardsToUpload = [];\n  for (const [shardId, entries] of Object.entries(shards)) {\n    const data: ShardData = {\n      // biome-ignore lint/style/noNonNullAssertion: we know this is in bounds\n      proofs: tree.getHexProof(roots[shardId]!),\n      entries,\n    };\n    shardsToUpload.push({\n      data: stringify(data),\n      name: `${shardId}.json`,\n    });\n  }\n  let uris = await upload({\n    client: options.client,\n    files: shardsToUpload,\n  });\n  // in the case of just 1 shard -> upload returns a string, not an array\n  if (!Array.isArray(uris)) {\n    uris = [uris];\n  }\n  if (uris.length === 0) {\n    throw new Error(\"No URIs returned from uploading merkle tree shards\");\n  }\n  // biome-ignore lint/style/noNonNullAssertion: throws above if no URIs\n  const baseUri = uris[0]!.slice(0, uris[0]!.lastIndexOf(\"/\"));\n  // 6. Also upload the original entries for retrieving all entries\n  const originalEntriesUri = await upload({\n    client: options.client,\n    files: [stringify(options.overrides)],\n  });\n  // 7. assmeble the final sharded merkle tree info\n  const shardedMerkleInfo: ShardedMerkleTreeInfo = {\n    merkleRoot: tree.getHexRoot(),\n    baseUri,\n    originalEntriesUri,\n    shardNybbles,\n    tokenDecimals: options.tokenDecimals,\n    isShardedMerkleTree: true,\n  };\n  // 8. upload the final sharded merkle tree info\n  const finalUri = await upload({\n    client: options.client,\n    files: [shardedMerkleInfo],\n  });\n  return {\n    shardedMerkleInfo,\n    uri: finalUri,\n  };\n}\n", "import { maxUint256 } from \"ox/Solidity\";\nimport { NATIVE_TOKEN_ADDRESS } from \"../../../constants/addresses.js\";\nimport type { ThirdwebContract } from \"../../../contract/contract.js\";\nimport type { SetClaimConditionsParams as GeneratedParams } from \"../../../extensions/erc1155/__generated__/IDrop1155/write/setClaimConditions.js\";\nimport { upload } from \"../../../storage/upload.js\";\nimport { dateToSeconds } from \"../../date.js\";\nimport { type Hex, toHex } from \"../../encoding/hex.js\";\nimport { convertErc20Amount } from \"../convert-erc20-amount.js\";\nimport { processOverrideList } from \"./process-override-list.js\";\nimport type { ClaimConditionsInput } from \"./types.js\";\n\nexport async function getMulticallSetClaimConditionTransactions(options: {\n  phases: ClaimConditionsInput[];\n  contract: ThirdwebContract;\n  tokenDecimals: number;\n  tokenId?: bigint;\n  resetClaimEligibility?: boolean;\n  singlePhase?: boolean;\n}): Promise<Hex[]> {\n  const merkleInfos: Record<string, string> = {};\n  const phases = await Promise.all(\n    options.phases.map(async (phase) => {\n      // allowlist\n      let merkleRoot: string = phase.merkleRootHash || toHex(\"\", { size: 32 });\n      if (phase.overrideList) {\n        const { shardedMerkleInfo, uri } = await processOverrideList({\n          overrides: phase.overrideList,\n          client: options.contract.client,\n          chain: options.contract.chain,\n          tokenDecimals: options.tokenDecimals,\n        });\n        merkleInfos[shardedMerkleInfo.merkleRoot] = uri;\n        merkleRoot = shardedMerkleInfo.merkleRoot;\n      }\n      // metadata\n      let metadata = \"\";\n      if (phase.metadata && typeof phase.metadata === \"string\") {\n        metadata = phase.metadata;\n      } else if (phase.metadata && typeof phase.metadata === \"object\") {\n        metadata = await upload({\n          client: options.contract.client,\n          files: [phase.metadata],\n        });\n      }\n      return {\n        startTimestamp: dateToSeconds(phase.startTime ?? new Date(0)),\n        currency: phase.currencyAddress || NATIVE_TOKEN_ADDRESS,\n        pricePerToken: await convertErc20Amount({\n          chain: options.contract.chain,\n          client: options.contract.client,\n          erc20Address: phase.currencyAddress || NATIVE_TOKEN_ADDRESS,\n          amount: phase.price?.toString() ?? \"0\",\n        }),\n        maxClaimableSupply: phase.maxClaimableSupply ?? maxUint256,\n        quantityLimitPerWallet: phase.maxClaimablePerWallet ?? maxUint256,\n        merkleRoot,\n        metadata,\n        supplyClaimed: 0n,\n      } as GeneratedParams[\"phases\"][number];\n    }),\n  );\n  const encodedTransactions: Hex[] = [];\n  // if we have new merkle roots, we need to upload them to the contract metadata\n  if (Object.keys(merkleInfos).length > 0) {\n    const [{ getContractMetadata }, { encodeSetContractURI }] =\n      await Promise.all([\n        import(\"../../../extensions/common/read/getContractMetadata.js\"),\n        import(\n          \"../../../extensions/common/__generated__/IContractMetadata/write/setContractURI.js\"\n        ),\n      ]);\n    const metadata = await getContractMetadata({\n      contract: options.contract,\n    });\n    // keep the old merkle roots from other tokenIds\n    for (const key of Object.keys(metadata.merkle || {})) {\n      merkleInfos[key] = metadata.merkle[key];\n    }\n    const mergedMetadata = {\n      ...metadata,\n      merkle: merkleInfos,\n    };\n    const uri = await upload({\n      client: options.contract.client,\n      files: [mergedMetadata],\n    });\n    const encodedSetContractURI = encodeSetContractURI({\n      uri,\n    });\n    encodedTransactions.push(encodedSetContractURI);\n  }\n  const sortedPhases = phases.sort((a, b) =>\n    Number(a.startTimestamp - b.startTimestamp),\n  );\n  let encodedSetClaimConditions: Hex;\n  if (options.tokenId !== undefined) {\n    // 1155\n    if (options.singlePhase) {\n      const { encodeSetClaimConditions } = await import(\n        \"../../../extensions/erc1155/__generated__/IDropSinglePhase1155/write/setClaimConditions.js\"\n      );\n      const phase = sortedPhases[0];\n      if (!phase) {\n        throw new Error(\"No phase provided\");\n      }\n      encodedSetClaimConditions = encodeSetClaimConditions({\n        tokenId: options.tokenId,\n        phase,\n        resetClaimEligibility: options.resetClaimEligibility || false,\n      });\n    } else {\n      const { encodeSetClaimConditions } = await import(\n        \"../../../extensions/erc1155/__generated__/IDrop1155/write/setClaimConditions.js\"\n      );\n      encodedSetClaimConditions = encodeSetClaimConditions({\n        tokenId: options.tokenId,\n        phases: sortedPhases,\n        resetClaimEligibility: options.resetClaimEligibility || false,\n      });\n    }\n  } else {\n    // erc721 or erc20\n    if (options.singlePhase) {\n      const { encodeSetClaimConditions } = await import(\n        \"../../../extensions/erc721/__generated__/IDropSinglePhase/write/setClaimConditions.js\"\n      );\n      const phase = sortedPhases[0];\n      if (!phase) {\n        throw new Error(\"No phase provided\");\n      }\n      encodedSetClaimConditions = encodeSetClaimConditions({\n        phase,\n        resetClaimEligibility: options.resetClaimEligibility || false,\n      });\n    } else {\n      const { encodeSetClaimConditions } = await import(\n        \"../../../extensions/erc721/__generated__/IDrop/write/setClaimConditions.js\"\n      );\n      encodedSetClaimConditions = encodeSetClaimConditions({\n        phases: sortedPhases,\n        resetClaimEligibility: options.resetClaimEligibility || false,\n      });\n    }\n  }\n  encodedTransactions.push(encodedSetClaimConditions);\n  return encodedTransactions;\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"setClaimConditions\" function.\n */\nexport type SetClaimConditionsParams = WithOverrides<{\n  phases: AbiParameterToPrimitiveType<{\n    type: \"tuple[]\";\n    name: \"phases\";\n    components: [\n      { type: \"uint256\"; name: \"startTimestamp\" },\n      { type: \"uint256\"; name: \"maxClaimableSupply\" },\n      { type: \"uint256\"; name: \"supplyClaimed\" },\n      { type: \"uint256\"; name: \"quantityLimitPerWallet\" },\n      { type: \"bytes32\"; name: \"merkleRoot\" },\n      { type: \"uint256\"; name: \"pricePerToken\" },\n      { type: \"address\"; name: \"currency\" },\n      { type: \"string\"; name: \"metadata\" },\n    ];\n  }>;\n  resetClaimEligibility: AbiParameterToPrimitiveType<{\n    type: \"bool\";\n    name: \"resetClaimEligibility\";\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0x74bc7db7\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple[]\",\n    name: \"phases\",\n    components: [\n      {\n        type: \"uint256\",\n        name: \"startTimestamp\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxClaimableSupply\",\n      },\n      {\n        type: \"uint256\",\n        name: \"supplyClaimed\",\n      },\n      {\n        type: \"uint256\",\n        name: \"quantityLimitPerWallet\",\n      },\n      {\n        type: \"bytes32\",\n        name: \"merkleRoot\",\n      },\n      {\n        type: \"uint256\",\n        name: \"pricePerToken\",\n      },\n      {\n        type: \"address\",\n        name: \"currency\",\n      },\n      {\n        type: \"string\",\n        name: \"metadata\",\n      },\n    ],\n  },\n  {\n    type: \"bool\",\n    name: \"resetClaimEligibility\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `setClaimConditions` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `setClaimConditions` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isSetClaimConditionsSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isSetClaimConditionsSupported([\"0x...\"]);\n * ```\n */\nexport function isSetClaimConditionsSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"setClaimConditions\" function.\n * @param options - The options for the setClaimConditions function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeSetClaimConditionsParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeSetClaimConditionsParams({\n *  phases: ...,\n *  resetClaimEligibility: ...,\n * });\n * ```\n */\nexport function encodeSetClaimConditionsParams(\n  options: SetClaimConditionsParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.phases,\n    options.resetClaimEligibility,\n  ]);\n}\n\n/**\n * Encodes the \"setClaimConditions\" function into a Hex string with its parameters.\n * @param options - The options for the setClaimConditions function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeSetClaimConditions } from \"thirdweb/extensions/erc20\";\n * const result = encodeSetClaimConditions({\n *  phases: ...,\n *  resetClaimEligibility: ...,\n * });\n * ```\n */\nexport function encodeSetClaimConditions(options: SetClaimConditionsParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeSetClaimConditionsParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"setClaimConditions\" function on the contract.\n * @param options - The options for the \"setClaimConditions\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { setClaimConditions } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = setClaimConditions({\n *  contract,\n *  phases: ...,\n *  resetClaimEligibility: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function setClaimConditions(\n  options: BaseTransactionOptions<\n    | SetClaimConditionsParams\n    | {\n        asyncParams: () => Promise<SetClaimConditionsParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.phases,\n        resolvedOptions.resetClaimEligibility,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { BaseTransactionOptions } from \"../../../../transaction/types.js\";\nimport { getMulticallSetClaimConditionTransactions } from \"../../../../utils/extensions/drops/get-multicall-set-claim-claim-conditon-transactions.js\";\nimport type { ClaimConditionsInput } from \"../../../../utils/extensions/drops/types.js\";\nimport { isSetContractURISupported } from \"../../../common/__generated__/IContractMetadata/write/setContractURI.js\";\nimport {\n  isMulticallSupported,\n  multicall,\n} from \"../../../common/__generated__/IMulticall/write/multicall.js\";\nimport { isGetContractMetadataSupported } from \"../../../common/read/getContractMetadata.js\";\nimport { isSetClaimConditionsSupported as isSetClaimConditionsSupportedGenerated } from \"../../__generated__/IDropERC20/write/setClaimConditions.js\";\nimport { decimals, isDecimalsSupported } from \"../../read/decimals.js\";\n\n/**\n * @extension ERC20\n */\nexport type SetClaimConditionsParams = {\n  phases: ClaimConditionsInput[];\n  resetClaimEligibility?: boolean;\n  singlePhaseDrop?: boolean;\n};\n\n/**\n * Set the claim conditions for a ERC20 drop\n * This method is only available on the `DropERC20` contract.\n * @param options\n * @returns the prepared transaction\n * @extension ERC20\n * @example\n * ```ts\n * import { setClaimConditions } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = setClaimConditions({\n *  contract,\n *  phases: [\n *    {\n *      maxClaimableSupply: 100n,\n *      maxClaimablePerWallet: 1n,\n *      currencyAddress: \"0x...\",\n *      price: 0.1,\n *      startTime: new Date(),\n *    },\n *   ],\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function setClaimConditions(\n  options: BaseTransactionOptions<SetClaimConditionsParams>,\n) {\n  return multicall({\n    contract: options.contract,\n    asyncParams: async () => {\n      return {\n        data: await getMulticallSetClaimConditionTransactions({\n          contract: options.contract,\n          phases: options.phases,\n          resetClaimEligibility: options.resetClaimEligibility,\n          tokenDecimals: await decimals({ contract: options.contract }),\n          singlePhase: options.singlePhaseDrop,\n        }),\n      };\n    },\n  });\n}\n\n/**\n * Checks if the `setClaimConditions` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `setClaimConditions` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isSetClaimConditionsSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isSetClaimConditionsSupported([\"0x...\"]);\n * ```\n */\nexport function isSetClaimConditionsSupported(availableSelectors: string[]) {\n  return (\n    isMulticallSupported(availableSelectors) &&\n    // needed for setting contract metadata\n    isGetContractMetadataSupported(availableSelectors) &&\n    isSetContractURISupported(availableSelectors) &&\n    // needed for decimals\n    isDecimalsSupported(availableSelectors) &&\n    // needs to actually be able to set the claim Conditions\n    isSetClaimConditionsSupportedGenerated(availableSelectors)\n  );\n}\n", "import type { Hex } from \"viem\";\nimport type { BaseTransactionOptions } from \"../../../../transaction/types.js\";\nimport type { ClaimCondition } from \"../../../../utils/extensions/drops/types.js\";\nimport {\n  isSetClaimConditionsSupported,\n  setClaimConditions,\n} from \"../../__generated__/IDropERC20/write/setClaimConditions.js\";\nimport {\n  getClaimConditions,\n  isGetClaimConditionsSupported,\n} from \"../read/getClaimConditions.js\";\n\n/**\n * Reset the claim eligibility for all users.\n * This method is only available on the `DropERC20` contract.\n * @param options\n * @returns the prepared transaction\n * @extension ERC20\n * @example\n * ```ts\n * import { resetClaimEligibility } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = resetClaimEligibility({\n *  contract,\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function resetClaimEligibility(options: BaseTransactionOptions) {\n  // download existing conditions\n  return setClaimConditions({\n    contract: options.contract,\n    asyncParams: async () => {\n      // get existing conditions\n      const existingConditions = await getClaimConditions(options);\n\n      // then simply return the exact same ones, but with the resetClaimEligibility flag set to true\n      return {\n        // type is necessary because of viem hex shenanigans (strict vs non-strict `0x` prefix string)\n        phases: existingConditions as Array<\n          ClaimCondition & {\n            currency: Hex;\n            merkleRoot: Hex;\n          }\n        >,\n        resetClaimEligibility: true,\n      };\n    },\n  });\n}\n\n/**\n * Checks if the `resetClaimEligibility` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `resetClaimEligibility` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isResetClaimEligibilitySupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isResetClaimEligibilitySupported([\"0x...\"]);\n * ```\n */\nexport function isResetClaimEligibilitySupported(availableSelectors: string[]) {\n  return (\n    isGetClaimConditionsSupported(availableSelectors) &&\n    isSetClaimConditionsSupported(availableSelectors)\n  );\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"mintWithSignature\" function.\n */\nexport type MintWithSignatureParams = WithOverrides<{\n  payload: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"payload\";\n    components: [\n      { type: \"address\"; name: \"to\" },\n      { type: \"address\"; name: \"primarySaleRecipient\" },\n      { type: \"uint256\"; name: \"quantity\" },\n      { type: \"uint256\"; name: \"price\" },\n      { type: \"address\"; name: \"currency\" },\n      { type: \"uint128\"; name: \"validityStartTimestamp\" },\n      { type: \"uint128\"; name: \"validityEndTimestamp\" },\n      { type: \"bytes32\"; name: \"uid\" },\n    ];\n  }>;\n  signature: AbiParameterToPrimitiveType<{ type: \"bytes\"; name: \"signature\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x8f0fefbb\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple\",\n    name: \"payload\",\n    components: [\n      {\n        type: \"address\",\n        name: \"to\",\n      },\n      {\n        type: \"address\",\n        name: \"primarySaleRecipient\",\n      },\n      {\n        type: \"uint256\",\n        name: \"quantity\",\n      },\n      {\n        type: \"uint256\",\n        name: \"price\",\n      },\n      {\n        type: \"address\",\n        name: \"currency\",\n      },\n      {\n        type: \"uint128\",\n        name: \"validityStartTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"validityEndTimestamp\",\n      },\n      {\n        type: \"bytes32\",\n        name: \"uid\",\n      },\n    ],\n  },\n  {\n    type: \"bytes\",\n    name: \"signature\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `mintWithSignature` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `mintWithSignature` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isMintWithSignatureSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isMintWithSignatureSupported([\"0x...\"]);\n * ```\n */\nexport function isMintWithSignatureSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"mintWithSignature\" function.\n * @param options - The options for the mintWithSignature function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeMintWithSignatureParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeMintWithSignatureParams({\n *  payload: ...,\n *  signature: ...,\n * });\n * ```\n */\nexport function encodeMintWithSignatureParams(\n  options: MintWithSignatureParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [options.payload, options.signature]);\n}\n\n/**\n * Encodes the \"mintWithSignature\" function into a Hex string with its parameters.\n * @param options - The options for the mintWithSignature function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeMintWithSignature } from \"thirdweb/extensions/erc20\";\n * const result = encodeMintWithSignature({\n *  payload: ...,\n *  signature: ...,\n * });\n * ```\n */\nexport function encodeMintWithSignature(options: MintWithSignatureParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeMintWithSignatureParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"mintWithSignature\" function on the contract.\n * @param options - The options for the \"mintWithSignature\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { mintWithSignature } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = mintWithSignature({\n *  contract,\n *  payload: ...,\n *  signature: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function mintWithSignature(\n  options: BaseTransactionOptions<\n    | MintWithSignatureParams\n    | {\n        asyncParams: () => Promise<MintWithSignatureParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.payload, resolvedOptions.signature] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { AbiParameterToPrimitiveType, Address } from \"abitype\";\nimport {\n  NATIVE_TOKEN_ADDRESS,\n  isNativeTokenAddress,\n} from \"../../../constants/addresses.js\";\nimport type { ThirdwebContract } from \"../../../contract/contract.js\";\nimport type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { dateToSeconds, tenYearsFromNow } from \"../../../utils/date.js\";\nimport { type Hex, isHex, stringToHex } from \"../../../utils/encoding/hex.js\";\nimport { randomBytesHex } from \"../../../utils/random.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport { name } from \"../../common/read/name.js\";\nimport {\n  type MintWithSignatureParams,\n  mintWithSignature as generatedMintWithSignature,\n} from \"../__generated__/ISignatureMintERC20/write/mintWithSignature.js\";\n\n/**\n * Mints a new ERC20 token with the given minter signature\n * This method is only available on the `TokenERC20` contract.\n * @param options - The transaction options.\n * @extension ERC20\n * @example\n * ```ts\n * import { mintWithSignature, generateMintSignature } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const { payload, signature } = await generateMintSignature(...)\n *\n * const transaction = mintWithSignature({\n *   contract,\n *   payload,\n *   signature,\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n * @extension ERC20\n * @returns A promise that resolves to the transaction result.\n */\nexport function mintWithSignature(\n  options: BaseTransactionOptions<MintWithSignatureParams>,\n) {\n  const value = isNativeTokenAddress(options.payload.currency)\n    ? options.payload.price\n    : 0n;\n  const erc20Value =\n    !isNativeTokenAddress(options.payload.currency) &&\n    options.payload.price > 0n\n      ? {\n          amountWei: options.payload.price,\n          tokenAddress: options.payload.currency,\n        }\n      : undefined;\n  return generatedMintWithSignature({\n    ...options,\n    overrides: {\n      value,\n      erc20Value,\n    },\n  });\n}\n\n/**\n * @extension ERC20\n */\nexport type GenerateMintSignatureOptions = {\n  account: Account;\n  contract: ThirdwebContract;\n  mintRequest: GeneratePayloadInput;\n};\n\n/**\n * Generates the payload and signature for minting an ERC20 token.\n * @param options - The options for the minting process.\n * @example\n * ```ts\n * import { mintWithSignature, generateMintSignature } from \"thirdweb/extensions/erc20\";\n *\n * const { payload, signature } = await generateMintSignature({\n *   account,\n *   contract,\n *   mintRequest: {\n *     to: \"0x...\",\n *     quantity: \"10\",\n *   },\n * });\n *\n * const transaction = mintWithSignature({\n *   contract,\n *   payload,\n *   signature,\n * });\n * await sendTransaction({ transaction, account });\n * ```\n * @extension ERC20\n * @returns A promise that resolves to the payload and signature.\n */\nexport async function generateMintSignature(\n  options: GenerateMintSignatureOptions,\n) {\n  const { mintRequest, account, contract } = options;\n  const currency = mintRequest.currency || NATIVE_TOKEN_ADDRESS;\n  const [price, quantity, uid, tokenName] = await Promise.all([\n    // price per token in wei\n    (async () => {\n      // if priceInWei is provided, use it\n      if (\"priceInWei\" in mintRequest && mintRequest.priceInWei) {\n        return mintRequest.priceInWei;\n      }\n      // if price is provided, convert it to wei\n      if (\"price\" in mintRequest && mintRequest.price) {\n        const { convertErc20Amount } = await import(\n          \"../../../utils/extensions/convert-erc20-amount.js\"\n        );\n        return await convertErc20Amount({\n          amount: mintRequest.price,\n          client: contract.client,\n          chain: contract.chain,\n          erc20Address: currency,\n        });\n      }\n      // if neither price nor priceInWei is provided, default to 0\n      return 0n;\n    })(),\n    // quantity in wei\n    (async () => {\n      // if the quantity is already passed in wei, use it\n      if (\"quantityWei\" in mintRequest) {\n        return mintRequest.quantityWei;\n      }\n      // otherwise convert the quantity to wei using the contract's OWN decimals\n      const { convertErc20Amount } = await import(\n        \"../../../utils/extensions/convert-erc20-amount.js\"\n      );\n      return await convertErc20Amount({\n        amount: mintRequest.quantity,\n        client: contract.client,\n        chain: contract.chain,\n        erc20Address: contract.address,\n      });\n    })(),\n    ((): Hex => {\n      if (mintRequest.uid) {\n        return isHex(mintRequest.uid)\n          ? mintRequest.uid\n          : stringToHex(mintRequest.uid, { size: 32 });\n      }\n      return randomBytesHex();\n    })(),\n    // ERC20Permit (EIP-712) spec differs from signature mint 721, 1155.\n    // it uses the token name in the domain separator\n    name({\n      contract,\n    }),\n  ]);\n\n  const startTime = mintRequest.validityStartTimestamp || new Date(0);\n  const endTime = mintRequest.validityEndTimestamp || tenYearsFromNow();\n\n  const payload: PayloadType = {\n    price,\n    quantity,\n    uid,\n    currency,\n    to: mintRequest.to,\n    primarySaleRecipient: mintRequest.primarySaleRecipient || account.address,\n    validityStartTimestamp: dateToSeconds(startTime),\n    validityEndTimestamp: dateToSeconds(endTime),\n  };\n\n  const signature = await account.signTypedData({\n    domain: {\n      name: tokenName,\n      version: \"1\",\n      chainId: contract.chain.id,\n      verifyingContract: contract.address as Hex,\n    },\n    types: { MintRequest: MintRequest20 },\n    primaryType: \"MintRequest\",\n    message: payload,\n  });\n  return { payload, signature };\n}\n\ntype PayloadType = AbiParameterToPrimitiveType<{\n  type: \"tuple\";\n  name: \"payload\";\n  components: typeof MintRequest20;\n}>;\n\ntype GeneratePayloadInput = {\n  to: string;\n  primarySaleRecipient?: Address;\n  price?: string;\n  priceInWei?: bigint;\n  currency?: Address;\n  validityStartTimestamp?: Date;\n  validityEndTimestamp?: Date;\n  uid?: string;\n} & ({ quantity: string } | { quantityWei: bigint });\n\nconst MintRequest20 = [\n  { name: \"to\", type: \"address\" },\n  { name: \"primarySaleRecipient\", type: \"address\" },\n  { name: \"quantity\", type: \"uint256\" },\n  { name: \"price\", type: \"uint256\" },\n  { name: \"currency\", type: \"address\" },\n  { name: \"validityStartTimestamp\", type: \"uint128\" },\n  { name: \"validityEndTimestamp\", type: \"uint128\" },\n  { name: \"uid\", type: \"bytes32\" },\n] as const;\n", "import type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\n\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\nexport const FN_SELECTOR = \"0xd0e30db0\" as const;\nconst FN_INPUTS = [] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `deposit` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `deposit` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isDepositSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isDepositSupported([\"0x...\"]);\n * ```\n */\nexport function isDepositSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Prepares a transaction to call the \"deposit\" function on the contract.\n * @param options - The options for the \"deposit\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { deposit } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = deposit();\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function deposit(options: BaseTransactionOptions) {\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n", "import { prepareContractCall } from \"../../../transaction/prepare-contract-call.js\";\nimport type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { toWei } from \"../../../utils/units.js\";\nimport { FN_SELECTOR } from \"../__generated__/IWETH/write/deposit.js\";\n\n/**\n * @extension ERC20\n */\nexport type DepositParams =\n  | {\n      amount: string;\n    }\n  | { amountWei: bigint };\n\n/**\n * Calls the \"deposit\" function on the contract (useful to wrap ETH).\n * @param options - The options for the \"deposit\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { deposit } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = deposit({ contract, amount: \"0.1\" });\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function deposit(options: BaseTransactionOptions<DepositParams>) {\n  const value =\n    \"amountWei\" in options ? options.amountWei : toWei(options.amount);\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, [], []] as const,\n    value,\n    erc20Value: {\n      amountWei: value,\n      tokenAddress: options.contract.address,\n    },\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"withdraw\" function.\n */\nexport type WithdrawParams = WithOverrides<{\n  amount: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"amount\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x2e1a7d4d\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"amount\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `withdraw` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `withdraw` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isWithdrawSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isWithdrawSupported([\"0x...\"]);\n * ```\n */\nexport function isWithdrawSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"withdraw\" function.\n * @param options - The options for the withdraw function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeWithdrawParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeWithdrawParams({\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeWithdrawParams(options: WithdrawParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.amount]);\n}\n\n/**\n * Encodes the \"withdraw\" function into a Hex string with its parameters.\n * @param options - The options for the withdraw function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeWithdraw } from \"thirdweb/extensions/erc20\";\n * const result = encodeWithdraw({\n *  amount: ...,\n * });\n * ```\n */\nexport function encodeWithdraw(options: WithdrawParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeWithdrawParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"withdraw\" function on the contract.\n * @param options - The options for the \"withdraw\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { withdraw } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = withdraw({\n *  contract,\n *  amount: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function withdraw(\n  options: BaseTransactionOptions<\n    | WithdrawParams\n    | {\n        asyncParams: () => Promise<WithdrawParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.amount] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"Transfer\" event.\n */\nexport type TransferEventFilters = Partial<{\n  from: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"from\";\n    indexed: true;\n  }>;\n  to: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"to\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the Transfer event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC20\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { transferEvent } from \"thirdweb/extensions/erc20\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  transferEvent({\n *  from: ...,\n *  to: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function transferEvent(filters: TransferEventFilters = {}) {\n  return prepareEvent({\n    signature:\n      \"event Transfer(address indexed from, address indexed to, uint256 value)\",\n    filters,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"Approval\" event.\n */\nexport type ApprovalEventFilters = Partial<{\n  owner: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"owner\";\n    indexed: true;\n  }>;\n  spender: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"spender\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the Approval event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC20\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { approvalEvent } from \"thirdweb/extensions/erc20\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  approvalEvent({\n *  owner: ...,\n *  spender: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function approvalEvent(filters: ApprovalEventFilters = {}) {\n  return prepareEvent({\n    signature:\n      \"event Approval(address indexed owner, address indexed spender, uint256 value)\",\n    filters,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"TokensMinted\" event.\n */\nexport type TokensMintedEventFilters = Partial<{\n  mintedTo: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"mintedTo\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the TokensMinted event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC20\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { tokensMintedEvent } from \"thirdweb/extensions/erc20\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  tokensMintedEvent({\n *  mintedTo: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function tokensMintedEvent(filters: TokensMintedEventFilters = {}) {\n  return prepareEvent({\n    signature:\n      \"event TokensMinted(address indexed mintedTo, uint256 quantityMinted)\",\n    filters,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"TokensClaimed\" event.\n */\nexport type TokensClaimedEventFilters = Partial<{\n  claimConditionIndex: AbiParameterToPrimitiveType<{\n    type: \"uint256\";\n    name: \"claimConditionIndex\";\n    indexed: true;\n  }>;\n  claimer: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"claimer\";\n    indexed: true;\n  }>;\n  receiver: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"receiver\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the TokensClaimed event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC20\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { tokensClaimedEvent } from \"thirdweb/extensions/erc20\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  tokensClaimedEvent({\n *  claimConditionIndex: ...,\n *  claimer: ...,\n *  receiver: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function tokensClaimedEvent(filters: TokensClaimedEventFilters = {}) {\n  return prepareEvent({\n    signature:\n      \"event TokensClaimed(uint256 indexed claimConditionIndex, address indexed claimer, address indexed receiver, uint256 quantityClaimed)\",\n    filters,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\n\n/**\n * Creates an event object for the ClaimConditionsUpdated event.\n * @returns The prepared event object.\n * @extension ERC20\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { claimConditionsUpdatedEvent } from \"thirdweb/extensions/erc20\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  claimConditionsUpdatedEvent()\n * ],\n * });\n * ```\n */\nexport function claimConditionsUpdatedEvent() {\n  return prepareEvent({\n    signature:\n      \"event ClaimConditionsUpdated((uint256 startTimestamp, uint256 maxClaimableSupply, uint256 supplyClaimed, uint256 quantityLimitPerWallet, bytes32 merkleRoot, uint256 pricePerToken, address currency, string metadata)[] claimConditions, bool resetEligibility)\",\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"TokensMintedWithSignature\" event.\n */\nexport type TokensMintedWithSignatureEventFilters = Partial<{\n  signer: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"signer\";\n    indexed: true;\n  }>;\n  mintedTo: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"mintedTo\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the TokensMintedWithSignature event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC20\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { tokensMintedWithSignatureEvent } from \"thirdweb/extensions/erc20\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  tokensMintedWithSignatureEvent({\n *  signer: ...,\n *  mintedTo: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function tokensMintedWithSignatureEvent(\n  filters: TokensMintedWithSignatureEventFilters = {},\n) {\n  return prepareEvent({\n    signature:\n      \"event TokensMintedWithSignature(address indexed signer, address indexed mintedTo, (address to, address primarySaleRecipient, uint256 quantity, uint256 price, address currency, uint128 validityStartTimestamp, uint128 validityEndTimestamp, bytes32 uid) mintRequest)\",\n    filters,\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"delegate\" function.\n */\nexport type DelegateParams = WithOverrides<{\n  delegatee: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"delegatee\";\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0x5c19a95c\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"delegatee\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `delegate` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `delegate` method is supported.\n * @extension ERC20\n * @example\n * ```ts\n * import { isDelegateSupported } from \"thirdweb/extensions/erc20\";\n *\n * const supported = isDelegateSupported([\"0x...\"]);\n * ```\n */\nexport function isDelegateSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"delegate\" function.\n * @param options - The options for the delegate function.\n * @returns The encoded ABI parameters.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeDelegateParams } from \"thirdweb/extensions/erc20\";\n * const result = encodeDelegateParams({\n *  delegatee: ...,\n * });\n * ```\n */\nexport function encodeDelegateParams(options: DelegateParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.delegatee]);\n}\n\n/**\n * Encodes the \"delegate\" function into a Hex string with its parameters.\n * @param options - The options for the delegate function.\n * @returns The encoded hexadecimal string.\n * @extension ERC20\n * @example\n * ```ts\n * import { encodeDelegate } from \"thirdweb/extensions/erc20\";\n * const result = encodeDelegate({\n *  delegatee: ...,\n * });\n * ```\n */\nexport function encodeDelegate(options: DelegateParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeDelegateParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"delegate\" function on the contract.\n * @param options - The options for the \"delegate\" function.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { delegate } from \"thirdweb/extensions/erc20\";\n *\n * const transaction = delegate({\n *  contract,\n *  delegatee: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function delegate(\n  options: BaseTransactionOptions<\n    | DelegateParams\n    | {\n        asyncParams: () => Promise<DelegateParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.delegatee] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,cAAc;AAC3B,IAAM,YAAY,CAAA;AAClB,IAAM,aAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,uBAAuB,oBAA4B;AACjE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAgCA,eAAsB,YAAY,SAA+B;AAC/D,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAA;GACT;AACH;;;AClDO,IAAMA,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;;;AAgBJ,SAAU,wBAAwB,oBAA4B;AAClE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAyEM,SAAU,aACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO;QACL,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;;IAEpB;IACA,OAAO,YAAS;AApJpB;AAoJwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AArJzB;AAqJ6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAtJlB;AAsJsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAvJvB;AAuJ2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAxJ3B;AAwJ+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAzJnC;AA0JO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AA3JpB;AA2JwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AA5JvB;AA4J2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA7JzB;AA6J6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA9JhC;AA+JO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AC1IM,SAAU,QAAQ,oBAA4B;AAKlD,SAAO;IACL,gBAAgB,kBAAkB;IAClC,kBAAkB,kBAAkB;IACpC,oBAAoB,kBAAkB;IACtC,uBAAuB,kBAAkB;IACzC,qBAAqB,kBAAkB;IACvC,oBAAoB,kBAAkB;IACtC,wBAAwB,kBAAkB;IAC1C,mBAAmB,kBAAkB;IACrC,qBAAqB,kBAAkB;IACvC,MAAM,OAAO;AACjB;;;AC7BO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;;;AA4FV,eAAsB,UACpB,SAAgD;AAEhD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAC,QAAQ,OAAO;GACzB;AACH;;;AC1GO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa,CAAA;AAcb,SAAU,kBAAkB,oBAA4B;AAC5D,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAgEM,SAAU,OACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,IAAI,gBAAgB,MAAM;IACpD;IACA,OAAO,YAAS;AA9HpB;AA8HwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA/HzB;AA+H6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAhIlB;AAgIsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAjIvB;AAiI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAlI3B;AAkI+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAnInC;AAoIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AArIpB;AAqIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAtIvB;AAsI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAvIzB;AAuI6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AAxIhC;AAyIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;ACjGM,SAAUC,QAAO,SAA6C;AAClE,SAAO,OAAgB;IACrB,UAAU,QAAQ;IAClB,aAAa,YAAW;AACtB,UAAI;AACJ,UAAI,YAAY,SAAS;AAEvB,cAAM,EAAE,UAAAC,UAAQ,IAAK,MAAM,OAAO,wBAAqB;AAEvD,cAAM,IAAI,MAAMA,UAAS,OAAO,EAAE,MAAM,MAAM,EAAE;AAEhD,iBAAS,QAAQ,QAAQ,OAAO,SAAQ,GAAI,CAAC;MAC/C,OAAO;AACL,iBAAS,QAAQ;MACnB;AACA,aAAO;QACL,IAAI,QAAQ;QACZ;QACA,WAAW,QAAQ;;IAEvB;GACD;AACH;;;ACtBM,SAAUC,cACd,SAAmD;AAEnD,SAAO,aAAsB;IAC3B,UAAU,QAAQ;IAClB,aAAa,YAAW;AACtB,UAAI;AACJ,UAAI,YAAY,SAAS;AAEvB,cAAM,EAAE,UAAAC,UAAQ,IAAK,MAAM,OAAO,wBAAqB;AAEvD,cAAM,IAAI,MAAMA,UAAS,OAAO,EAAE,MAAM,MAAM,EAAE;AAEhD,iBAAS,QAAQ,QAAQ,OAAO,SAAQ,GAAI,CAAC;MAC/C,OAAO;AACL,iBAAS,QAAQ;MACnB;AACA,aAAO;QACL,MAAM,QAAQ;QACd,IAAI,QAAQ;QACZ,OAAO;QACP,WAAW;UACT,YAAY;YACV,WAAW;YACX,cAAc,QAAQ,SAAS;;UAEjC,GAAG,QAAQ;;;IAGjB;GACD;AACH;;;AC7DO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AAgBJ,SAAU,qBAAqB,oBAA4B;AAC/D,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AA+DM,SAAU,UACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,IAAI;IAC9B;IACA,OAAO,YAAS;AA7HpB;AA6HwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA9HzB;AA8H6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AA/HlB;AA+HsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAhIvB;AAgI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAjI3B;AAiI+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAlInC;AAmIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AApIpB;AAoIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AArIvB;AAqI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAtIzB;AAsI6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AAvIhC;AAwIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AC9FM,SAAU,cACd,SAAoD;AAEpD,SAAO,UAAU;IACf,UAAU,QAAQ;IAClB,aAAa,YAAW;AACtB,YAAM,UAAU,MAAM,wBAAwB,OAAO;AACrD,aAAO;QACL,MAAM,QAAQ,IAAI,CAAC,SAAQ;AACzB,iBAAO,eAAe;YACpB,IAAI,KAAK;YACT,OAAO,KAAK;YACZ,WAAW;cACT,YAAY;gBACV,WAAW,KAAK;gBAChB,cAAc,QAAQ,SAAS;;;WAGpC;QACH,CAAC;;IAEL;GACD;AACH;AA6BA,eAAsB,wBACpB,SAAoD;AAEpD,QAAM,iBAAiB,MAAM,QAAQ,MAAM,OACzC,OAAO,YAAY,WAAU;AAC3B,UAAM,MAAM,MAAM;AAClB,QAAI;AACJ,QAAI,YAAY,QAAQ;AAEtB,YAAM,EAAE,UAAAC,UAAQ,IAAK,MAAM,OAAO,wBAAqB;AAEvD,YAAM,IAAI,MAAMA,UAAS,OAAO,EAAE,MAAM,MAAM,MAAS;AACvD,UAAI,MAAM,QAAW;AACnB,cAAM,IAAI,MACR,4CAA4C,QAAQ,SAAS,OAAO,EAAE;MAE1E;AACA,oBAAc,QAAQ,OAAO,OAAO,SAAQ,GAAI,CAAC;IACnD,OAAO;AACL,oBAAc,OAAO;IACvB;AACA,UAAM,iBAAiB,IAAI,KACzB,CAAC,MAAM,EAAE,GAAG,YAAW,MAAO,OAAO,GAAG,YAAW,CAAE;AAEvD,QAAI,gBAAgB;AAClB,qBAAe,YAAY,eAAe,YAAY;IACxD,OAAO;AACL,UAAI,KAAK;QACP,IAAI,OAAO;QACX,WAAW;OACZ;IACH;AAEA,WAAO;EACT,GACA,QAAQ,QAAQ,CAAA,CAAyC,CAAC;AAE5D,SAAO;AACT;;;AC1HO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa,CAAA;AAgFb,SAAU,KACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,MAAM;IAChC;IACA,OAAO,YAAS;AAtHpB;AAsHwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AAvHzB;AAuH6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAxHlB;AAwHsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAzHvB;AAyH2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AA1H3B;AA0H+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA3HnC;AA4HO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AA7HpB;AA6HwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AA9HvB;AA8H2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA/HzB;AA+H6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AAhIhC;AAiIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;ACtHO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa,CAAA;AAmFb,SAAU,SACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,SAAS,gBAAgB,MAAM;IACzD;IACA,OAAO,YAAS;AA9HpB;AA8HwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA/HzB;AA+H6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAhIlB;AAgIsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAjIvB;AAiI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAlI3B;AAkI+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAnInC;AAoIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AArIpB;AAqIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAtIvB;AAsI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAvIzB;AAuI6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AAxIhC;AAyIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;ACzIO,IAAMC,eAAc;AAC3B,IAAMC,aAAY,CAAA;AAClB,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAeJ,SAAU,0BAA0B,oBAA4B;AACpE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,cAAaC,YAAWC,WAAU;GAC5C;AACH;AAgCA,eAAsB,eAAe,SAA+B;AAClE,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAA;GACT;AACH;;;ACxDA,eAAsB,mBACpB,SAA+B;AAE/B,MAAI;AACF,UAAM,CAAC,SAAS,KAAK,IAAI,MAAiB,eAAe,OAAO;AAEhE,UAAM,oBAEF,CAAA;AACJ,aAAS,IAAI,SAAS,IAAI,UAAU,OAAO,KAAK;AAC9C,wBAAkB,KACN,sBAAsB;QAC9B,GAAG;QACH,aAAa;OACd,CAAC;IAEN;AACA,WAAO,QAAQ,IAAI,iBAAiB;EACtC,QAAQ;AACN,UAAM,IAAI,MAAM,2BAA2B;EAC7C;AACF;AAcM,SAAU,8BAA8B,oBAA4B;AAExE,SACa,0BAA0B,kBAAkB,KAC7C,iCAAiC,kBAAkB;AAEjE;;;ACxBO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAKd,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AAkHV,eAAsB,YACpB,SAAkD;AAElD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ;MACN,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;;GAEX;AACH;;;AClLA,eAAsB,SACpB,SAA+C;AAE/C,QAAM,cAAc,OAAO,YAAW;AACpC,QAAI,mBAAmB,SAAS;AAC9B,aAAO,QAAQ;IACjB;AAEA,UAAM,EAAE,SAAAC,SAAO,IAAK,MAAM,OAAO,qBAA4B;AAC7D,WAAOA,SACL,QAAQ,UACR,MAAM,SAAS,EAAE,UAAU,QAAQ,SAAQ,CAAE,CAAC;EAElD,GAAE;AACF,QAAM,CAAC,aAAa,EAAE,UAAU,UAAU,eAAe,eAAc,CAAE,IACvE,MAAM,QAAQ,IAAI;IAChB,0BAA0B;MACxB,UAAU,QAAQ;KACnB;IACD,eAAe;MACb,UAAU,QAAQ;MAClB,UAAU;MACV,IAAI,QAAQ;MACZ,MAAM;MACN,MAAM,QAAQ;MACd,eAAe,MAAM,SAAS,EAAE,UAAU,QAAQ,SAAQ,CAAE;KAC7D;GACF;AACH,MAAI;AACF,UAAM,YAAY;MAChB,UAAU,QAAQ;MAClB,SAAS,QAAQ;MACjB;MACA;MACA;MACA;MACA;KACD;AACD,WAAO;MACL,QAAQ;;EAEZ,SAAS,OAAO;AACd,WAAO;MACL,QAAQ;MACR,QAAQ,MAAM,mBAAmB,EAAE,OAAO,UAAU,QAAQ,SAAQ,CAAE;;EAE1E;AACF;;;ACpEA,eAAsB,oBAAoB,SAYzC;AACC,QAAM,cAAc,QAAQ,aAAa;AACzC,QAAM,eAAe,QAAQ,gBAAgB;AAE7C,QAAM,SAA0C,CAAA;AAChD,aAAW,iBAAiB,QAAQ,WAAW;AAC7C,UAAM,QAAQ,cAAc,QACzB,MAAM,GAAG,IAAI,YAAY,EACzB,YAAW;AACd,QAAI,OAAO,KAAK,MAAM,QAAW;AAC/B,aAAO,KAAK,IAAI,CAAA;IAClB;AAEA,WAAO,KAAK,EAAG,KAAK,aAAa;EACnC;AAEA,QAAM,WAAW,MAAM,QAAQ,IAC7B,OAAO,QAAQ,MAAM,EAAE,IAAI,OAAO,CAAC,OAAO,OAAO,MAAM;IACrD;IACA,IAAI,WACF,MAAM,QAAQ,IACZ,QAAQ,IAAI,OAAO,UAAS;AAC1B,aAAO,YAAY;QACjB;QACA,OAAO,QAAQ;QACf,QAAQ,QAAQ;QAChB,eAAe,QAAQ;OACxB;IACH,CAAC,CAAC,CACH,EACD,WAAU;GACb,CAAC;AAGJ,QAAM,QAA6B,OAAO,YAAY,QAAQ;AAC9D,QAAM,OAAO,IAAI,WAAW,OAAO,OAAO,KAAK,CAAC;AAEhD,QAAM,iBAAiB,CAAA;AACvB,aAAW,CAAC,SAAS,OAAO,KAAK,OAAO,QAAQ,MAAM,GAAG;AACvD,UAAM,OAAkB;;MAEtB,QAAQ,KAAK,YAAY,MAAM,OAAO,CAAE;MACxC;;AAEF,mBAAe,KAAK;MAClB,MAAM,UAAU,IAAI;MACpB,MAAM,GAAG,OAAO;KACjB;EACH;AACA,MAAI,OAAO,MAAM,OAAO;IACtB,QAAQ,QAAQ;IAChB,OAAO;GACR;AAED,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,WAAO,CAAC,IAAI;EACd;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,IAAI,MAAM,oDAAoD;EACtE;AAEA,QAAM,UAAU,KAAK,CAAC,EAAG,MAAM,GAAG,KAAK,CAAC,EAAG,YAAY,GAAG,CAAC;AAE3D,QAAM,qBAAqB,MAAM,OAAO;IACtC,QAAQ,QAAQ;IAChB,OAAO,CAAC,UAAU,QAAQ,SAAS,CAAC;GACrC;AAED,QAAM,oBAA2C;IAC/C,YAAY,KAAK,WAAU;IAC3B;IACA;IACA;IACA,eAAe,QAAQ;IACvB,qBAAqB;;AAGvB,QAAM,WAAW,MAAM,OAAO;IAC5B,QAAQ,QAAQ;IAChB,OAAO,CAAC,iBAAiB;GAC1B;AACD,SAAO;IACL;IACA,KAAK;;AAET;;;ACnGA,eAAsB,0CAA0C,SAO/D;AACC,QAAM,cAAsC,CAAA;AAC5C,QAAM,SAAS,MAAM,QAAQ,IAC3B,QAAQ,OAAO,IAAI,OAAO,UAAS;AArBvC;AAuBM,QAAI,aAAqB,MAAM,kBAAkB,MAAM,IAAI,EAAE,MAAM,GAAE,CAAE;AACvE,QAAI,MAAM,cAAc;AACtB,YAAM,EAAE,mBAAmB,IAAG,IAAK,MAAM,oBAAoB;QAC3D,WAAW,MAAM;QACjB,QAAQ,QAAQ,SAAS;QACzB,OAAO,QAAQ,SAAS;QACxB,eAAe,QAAQ;OACxB;AACD,kBAAY,kBAAkB,UAAU,IAAI;AAC5C,mBAAa,kBAAkB;IACjC;AAEA,QAAI,WAAW;AACf,QAAI,MAAM,YAAY,OAAO,MAAM,aAAa,UAAU;AACxD,iBAAW,MAAM;IACnB,WAAW,MAAM,YAAY,OAAO,MAAM,aAAa,UAAU;AAC/D,iBAAW,MAAM,OAAO;QACtB,QAAQ,QAAQ,SAAS;QACzB,OAAO,CAAC,MAAM,QAAQ;OACvB;IACH;AACA,WAAO;MACL,gBAAgB,cAAc,MAAM,aAAa,oBAAI,KAAK,CAAC,CAAC;MAC5D,UAAU,MAAM,mBAAmB;MACnC,eAAe,MAAM,mBAAmB;QACtC,OAAO,QAAQ,SAAS;QACxB,QAAQ,QAAQ,SAAS;QACzB,cAAc,MAAM,mBAAmB;QACvC,UAAQ,WAAM,UAAN,mBAAa,eAAc;OACpC;MACD,oBAAoB,MAAM,sBAAsB;MAChD,wBAAwB,MAAM,yBAAyB;MACvD;MACA;MACA,eAAe;;EAEnB,CAAC,CAAC;AAEJ,QAAM,sBAA6B,CAAA;AAEnC,MAAI,OAAO,KAAK,WAAW,EAAE,SAAS,GAAG;AACvC,UAAM,CAAC,EAAE,oBAAmB,GAAI,EAAE,qBAAoB,CAAE,IACtD,MAAM,QAAQ,IAAI;MAChB,OAAO,mCAAwD;MAC/D,OACE,8BAAoF;KAEvF;AACH,UAAM,WAAW,MAAM,oBAAoB;MACzC,UAAU,QAAQ;KACnB;AAED,eAAW,OAAO,OAAO,KAAK,SAAS,UAAU,CAAA,CAAE,GAAG;AACpD,kBAAY,GAAG,IAAI,SAAS,OAAO,GAAG;IACxC;AACA,UAAM,iBAAiB;MACrB,GAAG;MACH,QAAQ;;AAEV,UAAM,MAAM,MAAM,OAAO;MACvB,QAAQ,QAAQ,SAAS;MACzB,OAAO,CAAC,cAAc;KACvB;AACD,UAAM,wBAAwB,qBAAqB;MACjD;KACD;AACD,wBAAoB,KAAK,qBAAqB;EAChD;AACA,QAAM,eAAe,OAAO,KAAK,CAAC,GAAG,MACnC,OAAO,EAAE,iBAAiB,EAAE,cAAc,CAAC;AAE7C,MAAI;AACJ,MAAI,QAAQ,YAAY,QAAW;AAEjC,QAAI,QAAQ,aAAa;AACvB,YAAM,EAAE,yBAAwB,IAAK,MAAM,OACzC,kCAA4F;AAE9F,YAAM,QAAQ,aAAa,CAAC;AAC5B,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mBAAmB;MACrC;AACA,kCAA4B,yBAAyB;QACnD,SAAS,QAAQ;QACjB;QACA,uBAAuB,QAAQ,yBAAyB;OACzD;IACH,OAAO;AACL,YAAM,EAAE,yBAAwB,IAAK,MAAM,OACzC,kCAAiF;AAEnF,kCAA4B,yBAAyB;QACnD,SAAS,QAAQ;QACjB,QAAQ;QACR,uBAAuB,QAAQ,yBAAyB;OACzD;IACH;EACF,OAAO;AAEL,QAAI,QAAQ,aAAa;AACvB,YAAM,EAAE,yBAAwB,IAAK,MAAM,OACzC,kCAAuF;AAEzF,YAAM,QAAQ,aAAa,CAAC;AAC5B,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mBAAmB;MACrC;AACA,kCAA4B,yBAAyB;QACnD;QACA,uBAAuB,QAAQ,yBAAyB;OACzD;IACH,OAAO;AACL,YAAM,EAAE,yBAAwB,IAAK,MAAM,OACzC,kCAA4E;AAE9E,kCAA4B,yBAAyB;QACnD,QAAQ;QACR,uBAAuB,QAAQ,yBAAyB;OACzD;IACH;EACF;AACA,sBAAoB,KAAK,yBAAyB;AAClD,SAAO;AACT;;;AChHO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa,CAAA;AAcb,SAAU,8BAA8B,oBAA4B;AACxE,SAAO,aAAa;IAClB;IACA,QAAQ,CAACF,eAAaC,aAAWC,YAAU;GAC5C;AACH;AAuEM,SAAU,mBACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO;QACL,gBAAgB;QAChB,gBAAgB;;IAEpB;IACA,OAAO,YAAS;AA1LpB;AA0LwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA3LzB;AA2L6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AA5LlB;AA4LsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AA7LvB;AA6L2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AA9L3B;AA8L+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA/LnC;AAgMO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAjMpB;AAiMwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAlMvB;AAkM2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAnMzB;AAmM6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AApMhC;AAqMO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AC5JM,SAAUC,oBACd,SAAyD;AAEzD,SAAO,UAAU;IACf,UAAU,QAAQ;IAClB,aAAa,YAAW;AACtB,aAAO;QACL,MAAM,MAAM,0CAA0C;UACpD,UAAU,QAAQ;UAClB,QAAQ,QAAQ;UAChB,uBAAuB,QAAQ;UAC/B,eAAe,MAAM,SAAS,EAAE,UAAU,QAAQ,SAAQ,CAAE;UAC5D,aAAa,QAAQ;SACtB;;IAEL;GACD;AACH;AAcM,SAAUC,+BAA8B,oBAA4B;AACxE,SACE,qBAAqB,kBAAkB;EAEvC,uBAA+B,kBAAkB,KACjD,0BAA0B,kBAAkB;EAE5C,oBAAoB,kBAAkB;EAEtC,8BAAuC,kBAAkB;AAE7D;;;AC5DM,SAAU,sBAAsB,SAA+B;AAEnE,SAAO,mBAAmB;IACxB,UAAU,QAAQ;IAClB,aAAa,YAAW;AAEtB,YAAM,qBAAqB,MAAM,mBAAmB,OAAO;AAG3D,aAAO;;QAEL,QAAQ;QAMR,uBAAuB;;IAE3B;GACD;AACH;AAcM,SAAU,iCAAiC,oBAA4B;AAC3E,SACE,8BAA8B,kBAAkB,KAChD,8BAA8B,kBAAkB;AAEpD;;;ACvCO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa,CAAA;AAuFb,SAAU,kBACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,SAAS,gBAAgB,SAAS;IAC5D;IACA,OAAO,YAAS;AAjLpB;AAiLwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AAlLzB;AAkL6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAnLlB;AAmLsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AApLvB;AAoL2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AArL3B;AAqL+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAtLnC;AAuLO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAxLpB;AAwLwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAzLvB;AAyL2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA1LzB;AA0L6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA3LhC;AA4LO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AC3JM,SAAUC,mBACd,SAAwD;AAExD,QAAM,QAAQ,qBAAqB,QAAQ,QAAQ,QAAQ,IACvD,QAAQ,QAAQ,QAChB;AACJ,QAAM,aACJ,CAAC,qBAAqB,QAAQ,QAAQ,QAAQ,KAC9C,QAAQ,QAAQ,QAAQ,KACpB;IACE,WAAW,QAAQ,QAAQ;IAC3B,cAAc,QAAQ,QAAQ;MAEhC;AACN,SAAO,kBAA2B;IAChC,GAAG;IACH,WAAW;MACT;MACA;;GAEH;AACH;AAqCA,eAAsB,sBACpB,SAAqC;AAErC,QAAM,EAAE,aAAa,SAAS,SAAQ,IAAK;AAC3C,QAAM,WAAW,YAAY,YAAY;AACzC,QAAM,CAAC,OAAO,UAAU,KAAK,SAAS,IAAI,MAAM,QAAQ,IAAI;;KAEzD,YAAW;AAEV,UAAI,gBAAgB,eAAe,YAAY,YAAY;AACzD,eAAO,YAAY;MACrB;AAEA,UAAI,WAAW,eAAe,YAAY,OAAO;AAC/C,cAAM,EAAE,oBAAAC,oBAAkB,IAAK,MAAM,OACnC,oCAAmD;AAErD,eAAO,MAAMA,oBAAmB;UAC9B,QAAQ,YAAY;UACpB,QAAQ,SAAS;UACjB,OAAO,SAAS;UAChB,cAAc;SACf;MACH;AAEA,aAAO;IACT,GAAE;;KAED,YAAW;AAEV,UAAI,iBAAiB,aAAa;AAChC,eAAO,YAAY;MACrB;AAEA,YAAM,EAAE,oBAAAA,oBAAkB,IAAK,MAAM,OACnC,oCAAmD;AAErD,aAAO,MAAMA,oBAAmB;QAC9B,QAAQ,YAAY;QACpB,QAAQ,SAAS;QACjB,OAAO,SAAS;QAChB,cAAc,SAAS;OACxB;IACH,GAAE;KACD,MAAU;AACT,UAAI,YAAY,KAAK;AACnB,eAAO,MAAM,YAAY,GAAG,IACxB,YAAY,MACZ,YAAY,YAAY,KAAK,EAAE,MAAM,GAAE,CAAE;MAC/C;AACA,aAAO,eAAc;IACvB,GAAE;;;IAGF,KAAK;MACH;KACD;GACF;AAED,QAAM,YAAY,YAAY,0BAA0B,oBAAI,KAAK,CAAC;AAClE,QAAM,UAAU,YAAY,wBAAwB,gBAAe;AAEnE,QAAM,UAAuB;IAC3B;IACA;IACA;IACA;IACA,IAAI,YAAY;IAChB,sBAAsB,YAAY,wBAAwB,QAAQ;IAClE,wBAAwB,cAAc,SAAS;IAC/C,sBAAsB,cAAc,OAAO;;AAG7C,QAAM,YAAY,MAAM,QAAQ,cAAc;IAC5C,QAAQ;MACN,MAAM;MACN,SAAS;MACT,SAAS,SAAS,MAAM;MACxB,mBAAmB,SAAS;;IAE9B,OAAO,EAAE,aAAa,cAAa;IACnC,aAAa;IACb,SAAS;GACV;AACD,SAAO,EAAE,SAAS,UAAS;AAC7B;AAmBA,IAAM,gBAAgB;EACpB,EAAE,MAAM,MAAM,MAAM,UAAS;EAC7B,EAAE,MAAM,wBAAwB,MAAM,UAAS;EAC/C,EAAE,MAAM,YAAY,MAAM,UAAS;EACnC,EAAE,MAAM,SAAS,MAAM,UAAS;EAChC,EAAE,MAAM,YAAY,MAAM,UAAS;EACnC,EAAE,MAAM,0BAA0B,MAAM,UAAS;EACjD,EAAE,MAAM,wBAAwB,MAAM,UAAS;EAC/C,EAAE,MAAM,OAAO,MAAM,UAAS;;;;AC7MzB,IAAMC,gBAAc;;;ACwBrB,SAAU,QAAQ,SAA8C;AACpE,QAAM,QACJ,eAAe,UAAU,QAAQ,YAAY,MAAM,QAAQ,MAAM;AACnE,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAa,CAAA,GAAI,CAAA,CAAE;IAC5B;IACA,YAAY;MACV,WAAW;MACX,cAAc,QAAQ,SAAS;;GAElC;AACH;;;ACxBO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa,CAAA;AAgFb,SAAU,SACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,MAAM;IAChC;IACA,OAAO,YAAS;AAtHpB;AAsHwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AAvHzB;AAuH6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAxHlB;AAwHsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAzHvB;AAyH2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AA1H3B;AA0H+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA3HnC;AA4HO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AA7HpB;AA6HwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AA9HvB;AA8H2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA/HzB;AA+H6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AAhIhC;AAiIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AChGM,SAAU,cAAc,UAAgC,CAAA,GAAE;AAC9D,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;ACNM,SAAU,cAAc,UAAgC,CAAA,GAAE;AAC9D,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;ACZM,SAAU,kBAAkB,UAAoC,CAAA,GAAE;AACtE,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;ACMM,SAAU,mBAAmB,UAAqC,CAAA,GAAE;AACxE,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;ACjCM,SAAU,8BAA2B;AACzC,SAAO,aAAa;IAClB,WACE;GACH;AACH;;;ACgBM,SAAU,+BACd,UAAiD,CAAA,GAAE;AAEnD,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;AC5BO,IAAMC,gBAAc;AAC3B,IAAMC,cAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,eAAa,CAAA;AAgFb,SAAU,SACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,eAAaC,aAAWC,YAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,SAAS;IACnC;IACA,OAAO,YAAS;AAzHpB;AAyHwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA1HzB;AA0H6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AA3HlB;AA2HsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AA5HvB;AA4H2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AA7H3B;AA6H+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA9HnC;AA+HO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAhIpB;AAgIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAjIvB;AAiI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAlIzB;AAkI6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AAnIhC;AAoIO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;", "names": ["FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "mintTo", "decimals", "transferFrom", "decimals", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "decimals", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "toUnits", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "setClaimConditions", "isSetClaimConditionsSupported", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "mintWithSignature", "convertErc20Amount", "FN_SELECTOR", "FN_SELECTOR", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS"]}