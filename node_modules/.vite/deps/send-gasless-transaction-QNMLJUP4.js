import {
  addTransactionToStore
} from "./chunk-DD6352NZ.js";
import "./chunk-DPFXQP4L.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/transaction/actions/gasless/send-gasless-transaction.js
async function sendGaslessTransaction({ account, transaction, serializableTransaction, gasless }) {
  if (serializableTransaction.value && serializableTransaction.value > 0n) {
    throw new Error("Gasless transactions cannot have a value");
  }
  let result;
  if (gasless.provider === "biconomy") {
    const { relayBiconomyTransaction } = await import("./biconomy-PFHWWYXT.js");
    result = await relayBiconomyTransaction({
      account,
      transaction,
      serializableTransaction,
      gasless
    });
  }
  if (gasless.provider === "openzeppelin") {
    const { relayOpenZeppelinTransaction } = await import("./openzeppelin-XP7CXYHJ.js");
    result = await relayOpenZeppelinTransaction({
      account,
      transaction,
      serializableTransaction,
      gasless
    });
  }
  if (gasless.provider === "engine") {
    const { relayEngineTransaction } = await import("./engine-KIBJK7GM.js");
    result = await relayEngineTransaction({
      account,
      transaction,
      serializableTransaction,
      gasless
    });
  }
  if (!result) {
    throw new Error("Unsupported gasless provider");
  }
  addTransactionToStore({
    address: account.address,
    transactionHash: result.transactionHash,
    chainId: transaction.chain.id
  });
  return result;
}
export {
  sendGaslessTransaction
};
//# sourceMappingURL=send-gasless-transaction-QNMLJUP4.js.map
