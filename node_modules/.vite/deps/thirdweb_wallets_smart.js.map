{"version": 3, "sources": ["../../thirdweb/src/wallets/smart/presets/index.ts", "../../thirdweb/src/extensions/erc7579/__generated__/IERC7579Account/write/execute.ts", "../../thirdweb/src/extensions/erc7579/__generated__/ModularAccountFactory/write/createAccountWithModules.ts", "../../thirdweb/src/wallets/smart/presets/7579.ts"], "sourcesContent": ["export { erc7579, type ERC7579Config } from \"./7579.js\";\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"execute\" function.\n */\nexport type ExecuteParams = WithOverrides<{\n  mode: AbiParameterToPrimitiveType<{ type: \"bytes32\"; name: \"mode\" }>;\n  executionCalldata: AbiParameterToPrimitiveType<{\n    type: \"bytes\";\n    name: \"executionCalldata\";\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0xe9ae5c53\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"bytes32\",\n    name: \"mode\",\n  },\n  {\n    type: \"bytes\",\n    name: \"executionCalldata\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `execute` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `execute` method is supported.\n * @extension ERC7579\n * @example\n * ```ts\n * import { isExecuteSupported } from \"thirdweb/extensions/erc7579\";\n *\n * const supported = isExecuteSupported([\"0x...\"]);\n * ```\n */\nexport function isExecuteSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"execute\" function.\n * @param options - The options for the execute function.\n * @returns The encoded ABI parameters.\n * @extension ERC7579\n * @example\n * ```ts\n * import { encodeExecuteParams } from \"thirdweb/extensions/erc7579\";\n * const result = encodeExecuteParams({\n *  mode: ...,\n *  executionCalldata: ...,\n * });\n * ```\n */\nexport function encodeExecuteParams(options: ExecuteParams) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.mode,\n    options.executionCalldata,\n  ]);\n}\n\n/**\n * Encodes the \"execute\" function into a Hex string with its parameters.\n * @param options - The options for the execute function.\n * @returns The encoded hexadecimal string.\n * @extension ERC7579\n * @example\n * ```ts\n * import { encodeExecute } from \"thirdweb/extensions/erc7579\";\n * const result = encodeExecute({\n *  mode: ...,\n *  executionCalldata: ...,\n * });\n * ```\n */\nexport function encodeExecute(options: ExecuteParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeExecuteParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"execute\" function on the contract.\n * @param options - The options for the \"execute\" function.\n * @returns A prepared transaction object.\n * @extension ERC7579\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { execute } from \"thirdweb/extensions/erc7579\";\n *\n * const transaction = execute({\n *  contract,\n *  mode: ...,\n *  executionCalldata: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function execute(\n  options: BaseTransactionOptions<\n    | ExecuteParams\n    | {\n        asyncParams: () => Promise<ExecuteParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.mode, resolvedOptions.executionCalldata] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"createAccountWithModules\" function.\n */\nexport type CreateAccountWithModulesParams = WithOverrides<{\n  owner: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"owner\" }>;\n  salt: AbiParameterToPrimitiveType<{ type: \"bytes\"; name: \"salt\" }>;\n  modules: AbiParameterToPrimitiveType<{\n    type: \"tuple[]\";\n    name: \"modules\";\n    components: [\n      { type: \"uint256\"; name: \"moduleTypeId\" },\n      { type: \"address\"; name: \"module\" },\n      { type: \"bytes\"; name: \"initData\" },\n    ];\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0x7c37d0dc\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"owner\",\n  },\n  {\n    type: \"bytes\",\n    name: \"salt\",\n  },\n  {\n    type: \"tuple[]\",\n    name: \"modules\",\n    components: [\n      {\n        type: \"uint256\",\n        name: \"moduleTypeId\",\n      },\n      {\n        type: \"address\",\n        name: \"module\",\n      },\n      {\n        type: \"bytes\",\n        name: \"initData\",\n      },\n    ],\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"address\",\n  },\n] as const;\n\n/**\n * Checks if the `createAccountWithModules` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `createAccountWithModules` method is supported.\n * @extension ERC7579\n * @example\n * ```ts\n * import { isCreateAccountWithModulesSupported } from \"thirdweb/extensions/erc7579\";\n *\n * const supported = isCreateAccountWithModulesSupported([\"0x...\"]);\n * ```\n */\nexport function isCreateAccountWithModulesSupported(\n  availableSelectors: string[],\n) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"createAccountWithModules\" function.\n * @param options - The options for the createAccountWithModules function.\n * @returns The encoded ABI parameters.\n * @extension ERC7579\n * @example\n * ```ts\n * import { encodeCreateAccountWithModulesParams } from \"thirdweb/extensions/erc7579\";\n * const result = encodeCreateAccountWithModulesParams({\n *  owner: ...,\n *  salt: ...,\n *  modules: ...,\n * });\n * ```\n */\nexport function encodeCreateAccountWithModulesParams(\n  options: CreateAccountWithModulesParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.owner,\n    options.salt,\n    options.modules,\n  ]);\n}\n\n/**\n * Encodes the \"createAccountWithModules\" function into a Hex string with its parameters.\n * @param options - The options for the createAccountWithModules function.\n * @returns The encoded hexadecimal string.\n * @extension ERC7579\n * @example\n * ```ts\n * import { encodeCreateAccountWithModules } from \"thirdweb/extensions/erc7579\";\n * const result = encodeCreateAccountWithModules({\n *  owner: ...,\n *  salt: ...,\n *  modules: ...,\n * });\n * ```\n */\nexport function encodeCreateAccountWithModules(\n  options: CreateAccountWithModulesParams,\n) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeCreateAccountWithModulesParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"createAccountWithModules\" function on the contract.\n * @param options - The options for the \"createAccountWithModules\" function.\n * @returns A prepared transaction object.\n * @extension ERC7579\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { createAccountWithModules } from \"thirdweb/extensions/erc7579\";\n *\n * const transaction = createAccountWithModules({\n *  contract,\n *  owner: ...,\n *  salt: ...,\n *  modules: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function createAccountWithModules(\n  options: BaseTransactionOptions<\n    | CreateAccountWithModulesParams\n    | {\n        asyncParams: () => Promise<CreateAccountWithModulesParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.owner,\n        resolvedOptions.salt,\n        resolvedOptions.modules,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import * as ox__AbiParameters from \"ox/AbiParameters\";\nimport * as ox__Hex from \"ox/Hex\";\nimport { serializeErc6492Signature } from \"../../../auth/serialize-erc6492-signature.js\";\nimport { verifyHash } from \"../../../auth/verify-hash.js\";\nimport { ZERO_ADDRESS } from \"../../../constants/addresses.js\";\nimport {\n  type ThirdwebContract,\n  getContract,\n} from \"../../../contract/contract.js\";\nimport { getNonce } from \"../../../extensions/erc4337/__generated__/IEntryPoint/read/getNonce.js\";\nimport { execute } from \"../../../extensions/erc7579/__generated__/IERC7579Account/write/execute.js\";\nimport { createAccountWithModules } from \"../../../extensions/erc7579/__generated__/ModularAccountFactory/write/createAccountWithModules.js\";\nimport { encode } from \"../../../transaction/actions/encode.js\";\nimport type { PreparedTransaction } from \"../../../transaction/prepare-transaction.js\";\nimport { readContract } from \"../../../transaction/read-contract.js\";\nimport { encodeAbiParameters } from \"../../../utils/abi/encodeAbiParameters.js\";\nimport { getAddress } from \"../../../utils/address.js\";\nimport { hashMessage } from \"../../../utils/hashing/hashMessage.js\";\nimport { hashTypedData } from \"../../../utils/hashing/hashTypedData.js\";\nimport type { Account } from \"../../interfaces/wallet.js\";\nimport { ENTRYPOINT_ADDRESS_v0_7 } from \"../lib/constants.js\";\nimport { generateRandomUint192 } from \"../lib/utils.js\";\nimport type { SmartWalletOptions } from \"../types.js\";\n\nexport type ERC7579Config = SmartWalletOptions & {\n  validatorAddress: string;\n  factoryAddress: string;\n};\n\n/**\n * Config for a ERC7579 modular smart wallet.\n *\n * This configuration is in BETA, expect breaking changes.\n *\n * @param options - Optional overrides for the smart wallet.\n * @returns The smart wallet options.\n *\n * @example\n * ```typescript\n * import { sepolia } from \"thirdweb/chains\";\n * import { smartWallet, Config } from \"thirdweb/wallets/smart\";\n *\n * const modularSmartWallet = smartWallet(\n *   Config.erc7579({\n *     chain: sepolia,\n *     sponsorGas: true,\n *     factoryAddress: \"0x...\", // the 7579 factory address\n *     validatorAddress: \"0x...\", // the default validator module address\n *   }),\n * });\n * ```\n *\n * @wallet\n * @extension ERC7579\n * @beta\n */\nexport function erc7579(options: ERC7579Config): SmartWalletOptions {\n  const saltHex =\n    options.overrides?.accountSalt &&\n    ox__Hex.validate(options.overrides.accountSalt)\n      ? options.overrides.accountSalt\n      : ox__Hex.fromString(options.overrides?.accountSalt ?? \"\");\n  const defaultValidator = getAddress(options.validatorAddress);\n  const modularAccountOptions: SmartWalletOptions = {\n    ...options,\n    factoryAddress: options.factoryAddress,\n    overrides: {\n      entrypointAddress: ENTRYPOINT_ADDRESS_v0_7,\n      createAccount(factoryContract, admin) {\n        // TODO (msa) - let ppl pass whatever modules they want here\n        return createAccountWithModules({\n          contract: factoryContract,\n          asyncParams: async () => {\n            // default validator\n            const modules = [\n              {\n                moduleTypeId: 1n, // validator type id\n                module: defaultValidator,\n                initData: ox__Hex.fromString(\"\"),\n              },\n            ];\n            return {\n              owner: admin,\n              salt: saltHex,\n              modules,\n            };\n          },\n        });\n      },\n      async predictAddress(factoryContract, admin) {\n        return readContract({\n          contract: factoryContract,\n          method:\n            \"function getAddress(address owner, bytes salt) returns (address)\",\n          params: [admin, saltHex],\n        });\n      },\n      execute(accountContract, transaction) {\n        return execute({\n          contract: accountContract,\n          async asyncParams() {\n            return {\n              mode: ox__Hex.padRight(\"0x00\", 32), // single execution\n              executionCalldata: ox__AbiParameters.encodePacked(\n                [\"address\", \"uint256\", \"bytes\"],\n                [\n                  transaction.to || ZERO_ADDRESS,\n                  transaction.value || 0n,\n                  transaction.data || \"0x\",\n                ],\n              ),\n            };\n          },\n        });\n      },\n      executeBatch(accountContract, transactions) {\n        return execute({\n          contract: accountContract,\n          async asyncParams() {\n            return {\n              mode: ox__Hex.padRight(\"0x01\", 32), // batch execution\n              executionCalldata: ox__AbiParameters.encode(\n                [\n                  {\n                    type: \"tuple[]\",\n                    components: [\n                      { type: \"address\", name: \"to\" },\n                      { type: \"uint256\", name: \"value\" },\n                      { type: \"bytes\", name: \"data\" },\n                    ],\n                  },\n                ],\n                [\n                  transactions.map((t) => ({\n                    to: t.to || ZERO_ADDRESS,\n                    value: t.value || 0n,\n                    data: t.data || \"0x\",\n                  })),\n                ],\n              ),\n            };\n          },\n        });\n      },\n      async getAccountNonce(accountContract) {\n        const entryPointNonce = await getNonce({\n          contract: getContract({\n            address: ENTRYPOINT_ADDRESS_v0_7,\n            chain: accountContract.chain,\n            client: accountContract.client,\n          }),\n          key: generateRandomUint192(),\n          sender: accountContract.address,\n        });\n        // TODO (msa) - could be different if validator for the deployed account is different\n        const withValidator = ox__Hex.from(\n          `${defaultValidator}${ox__Hex.fromNumber(entryPointNonce).slice(42)}`,\n        );\n        return ox__Hex.toBigInt(withValidator);\n      },\n      async signMessage(options) {\n        const { accountContract, factoryContract, adminAccount, message } =\n          options;\n        const originalMsgHash = hashMessage(message);\n        const createAccount = modularAccountOptions.overrides?.createAccount;\n        if (!createAccount) {\n          throw new Error(\"Create account override not provided\");\n        }\n        return generateSignature({\n          accountContract,\n          factoryContract,\n          adminAccount,\n          originalMsgHash,\n          defaultValidator,\n          createAccount,\n        });\n      },\n      async signTypedData(options) {\n        const { accountContract, factoryContract, adminAccount, typedData } =\n          options;\n        const originalMsgHash = hashTypedData(typedData);\n        const createAccount = modularAccountOptions.overrides?.createAccount;\n        if (!createAccount) {\n          throw new Error(\"Create account override not provided\");\n        }\n        return generateSignature({\n          accountContract,\n          factoryContract,\n          adminAccount,\n          originalMsgHash,\n          defaultValidator,\n          createAccount,\n        });\n      },\n      ...options.overrides,\n    },\n  };\n  return modularAccountOptions;\n}\n\nasync function generateSignature(options: {\n  accountContract: ThirdwebContract;\n  factoryContract: ThirdwebContract;\n  adminAccount: Account;\n  originalMsgHash: ox__Hex.Hex;\n  defaultValidator: string;\n  createAccount: (\n    factoryContract: ThirdwebContract,\n    admin: string,\n  ) => PreparedTransaction;\n}) {\n  const {\n    accountContract,\n    factoryContract,\n    adminAccount,\n    originalMsgHash,\n    defaultValidator,\n    createAccount,\n  } = options;\n  const wrappedMessageHash = encodeAbiParameters(\n    [{ type: \"bytes32\" }],\n    [originalMsgHash],\n  );\n\n  const rawSig = await adminAccount.signTypedData({\n    domain: {\n      // TODO (msa) - assumes our default validator here\n      name: \"DefaultValidator\",\n      version: \"1\",\n      chainId: accountContract.chain.id,\n      verifyingContract: defaultValidator,\n    },\n    primaryType: \"AccountMessage\",\n    types: { AccountMessage: [{ name: \"message\", type: \"bytes\" }] },\n    message: { message: wrappedMessageHash },\n  });\n\n  // add the validator address to the signature\n  const sig = encodeAbiParameters(\n    [{ type: \"address\" }, { type: \"bytes\" }],\n    [defaultValidator, rawSig],\n  );\n\n  const deployTx = createAccount(factoryContract, adminAccount.address);\n  if (!deployTx) {\n    throw new Error(\"Create account override not provided\");\n  }\n  const initCode = await encode(deployTx);\n  const erc6492Sig = serializeErc6492Signature({\n    address: factoryContract.address,\n    data: initCode,\n    signature: sig,\n  });\n\n  // check if the signature is valid\n  const isValid = await verifyHash({\n    hash: originalMsgHash,\n    signature: erc6492Sig,\n    address: accountContract.address,\n    chain: accountContract.chain,\n    client: accountContract.client,\n  });\n\n  if (!isValid) {\n    throw new Error(\n      `Something went wrong generating the signature for modular smart account: ${accountContract.address} on chain ${accountContract.chain.id}`,\n    );\n  }\n  return erc6492Sig;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;ACqBO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa,CAAA;AAsFb,SAAU,QACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,MAAM,gBAAgB,iBAAiB;IACjE;IACA,OAAO,YAAS;AApIpB;AAoIwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AArIzB;AAqI6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAtIlB;AAsIsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAvIvB;AAuI2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAxI3B;AAwI+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAzInC;AA0IO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AA3IpB;AA2IwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AA5IvB;AA4I2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AA7IzB;AA6I6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA9IhC;AA+IO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AC3HO,IAAMA,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAKd,IAAMC,cAAa;EACjB;IACE,MAAM;;;AAoGJ,SAAU,yBACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO;QACL,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;;IAEpB;IACA,OAAO,YAAS;AAhLpB;AAgLwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AAjLzB;AAiL6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AAlLlB;AAkLsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AAnLvB;AAmL2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AApL3B;AAoL+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AArLnC;AAsLO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAvLpB;AAuLwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAxLvB;AAwL2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAzLzB;AAyL6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AA1LhC;AA2LO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;AC1IM,SAAU,QAAQ,SAAsB;AAxD9C;AAyDE,QAAM,YACJ,aAAQ,cAAR,mBAAmB,gBACX,SAAS,QAAQ,UAAU,WAAW,IAC1C,QAAQ,UAAU,cACV,aAAW,aAAQ,cAAR,mBAAmB,gBAAe,EAAE;AAC7D,QAAM,mBAAmB,WAAW,QAAQ,gBAAgB;AAC5D,QAAM,wBAA4C;IAChD,GAAG;IACH,gBAAgB,QAAQ;IACxB,WAAW;MACT,mBAAmB;MACnB,cAAc,iBAAiB,OAAK;AAElC,eAAO,yBAAyB;UAC9B,UAAU;UACV,aAAa,YAAW;AAEtB,kBAAM,UAAU;cACd;gBACE,cAAc;;gBACd,QAAQ;gBACR,UAAkB,WAAW,EAAE;;;AAGnC,mBAAO;cACL,OAAO;cACP,MAAM;cACN;;UAEJ;SACD;MACH;MACA,MAAM,eAAe,iBAAiB,OAAK;AACzC,eAAO,aAAa;UAClB,UAAU;UACV,QACE;UACF,QAAQ,CAAC,OAAO,OAAO;SACxB;MACH;MACA,QAAQ,iBAAiB,aAAW;AAClC,eAAO,QAAQ;UACb,UAAU;UACV,MAAM,cAAW;AACf,mBAAO;cACL,MAAc,SAAS,QAAQ,EAAE;;cACjC,mBAAqC,aACnC,CAAC,WAAW,WAAW,OAAO,GAC9B;gBACE,YAAY,MAAM;gBAClB,YAAY,SAAS;gBACrB,YAAY,QAAQ;eACrB;;UAGP;SACD;MACH;MACA,aAAa,iBAAiB,cAAY;AACxC,eAAO,QAAQ;UACb,UAAU;UACV,MAAM,cAAW;AACf,mBAAO;cACL,MAAc,SAAS,QAAQ,EAAE;;cACjC,mBAAqCC,QACnC;gBACE;kBACE,MAAM;kBACN,YAAY;oBACV,EAAE,MAAM,WAAW,MAAM,KAAI;oBAC7B,EAAE,MAAM,WAAW,MAAM,QAAO;oBAChC,EAAE,MAAM,SAAS,MAAM,OAAM;;;iBAInC;gBACE,aAAa,IAAI,CAAC,OAAO;kBACvB,IAAI,EAAE,MAAM;kBACZ,OAAO,EAAE,SAAS;kBAClB,MAAM,EAAE,QAAQ;kBAChB;eACH;;UAGP;SACD;MACH;MACA,MAAM,gBAAgB,iBAAe;AACnC,cAAM,kBAAkB,MAAM,SAAS;UACrC,UAAU,YAAY;YACpB,SAAS;YACT,OAAO,gBAAgB;YACvB,QAAQ,gBAAgB;WACzB;UACD,KAAK,sBAAqB;UAC1B,QAAQ,gBAAgB;SACzB;AAED,cAAM,gBAAwB,KAC5B,GAAG,gBAAgB,GAAW,WAAW,eAAe,EAAE,MAAM,EAAE,CAAC,EAAE;AAEvE,eAAe,SAAS,aAAa;MACvC;MACA,MAAM,YAAYC,UAAO;AAhK/B,YAAAC;AAiKQ,cAAM,EAAE,iBAAiB,iBAAiB,cAAc,QAAO,IAC7DD;AACF,cAAM,kBAAkB,YAAY,OAAO;AAC3C,cAAM,iBAAgBC,MAAA,sBAAsB,cAAtB,gBAAAA,IAAiC;AACvD,YAAI,CAAC,eAAe;AAClB,gBAAM,IAAI,MAAM,sCAAsC;QACxD;AACA,eAAO,kBAAkB;UACvB;UACA;UACA;UACA;UACA;UACA;SACD;MACH;MACA,MAAM,cAAcD,UAAO;AAjLjC,YAAAC;AAkLQ,cAAM,EAAE,iBAAiB,iBAAiB,cAAc,UAAS,IAC/DD;AACF,cAAM,kBAAkB,cAAc,SAAS;AAC/C,cAAM,iBAAgBC,MAAA,sBAAsB,cAAtB,gBAAAA,IAAiC;AACvD,YAAI,CAAC,eAAe;AAClB,gBAAM,IAAI,MAAM,sCAAsC;QACxD;AACA,eAAO,kBAAkB;UACvB;UACA;UACA;UACA;UACA;UACA;SACD;MACH;MACA,GAAG,QAAQ;;;AAGf,SAAO;AACT;AAEA,eAAe,kBAAkB,SAUhC;AACC,QAAM,EACJ,iBACA,iBACA,cACA,iBACA,kBACA,cAAa,IACX;AACJ,QAAM,qBAAqB,oBACzB,CAAC,EAAE,MAAM,UAAS,CAAE,GACpB,CAAC,eAAe,CAAC;AAGnB,QAAM,SAAS,MAAM,aAAa,cAAc;IAC9C,QAAQ;;MAEN,MAAM;MACN,SAAS;MACT,SAAS,gBAAgB,MAAM;MAC/B,mBAAmB;;IAErB,aAAa;IACb,OAAO,EAAE,gBAAgB,CAAC,EAAE,MAAM,WAAW,MAAM,QAAO,CAAE,EAAC;IAC7D,SAAS,EAAE,SAAS,mBAAkB;GACvC;AAGD,QAAM,MAAM,oBACV,CAAC,EAAE,MAAM,UAAS,GAAI,EAAE,MAAM,QAAO,CAAE,GACvC,CAAC,kBAAkB,MAAM,CAAC;AAG5B,QAAM,WAAW,cAAc,iBAAiB,aAAa,OAAO;AACpE,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,sCAAsC;EACxD;AACA,QAAM,WAAW,MAAM,OAAO,QAAQ;AACtC,QAAM,aAAa,0BAA0B;IAC3C,SAAS,gBAAgB;IACzB,MAAM;IACN,WAAW;GACZ;AAGD,QAAM,UAAU,MAAM,WAAW;IAC/B,MAAM;IACN,WAAW;IACX,SAAS,gBAAgB;IACzB,OAAO,gBAAgB;IACvB,QAAQ,gBAAgB;GACzB;AAED,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MACR,4EAA4E,gBAAgB,OAAO,aAAa,gBAAgB,MAAM,EAAE,EAAE;EAE9I;AACA,SAAO;AACT;", "names": ["FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "encode", "options", "_a"]}